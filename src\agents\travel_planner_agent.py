"""
旅行规划Agent核心实现

基于PRD需求实现的智能旅行规划Agent，支持意图理解、工具规划、并行执行等功能。
"""
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, AsyncGenerator
import re

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, AgentState, StreamEvent,
    EventType, ThinkingCategory, ThinkingStepPayload, ToolCallPayload,
    ToolResultPayload, POIInfo, DailyPlan, TripSummary, WeatherInfo,
    Location, MapInfo, BudgetEstimation, BudgetBreakdown, UserProfile
)
from src.tools.amap_mcp_client import get_amap_client
import asyncio

from src.core.llm_manager import LLMManager
from src.core.config import get_settings
from tools.Amap.map_tool import MapTool

# 导入MySQL CRUD系统
from src.models.mysql_crud import (
    user_crud, user_memory_crud, user_summary_crud,
    ai_planning_session_crud, itinerary_crud
)
from src.models.mysql_models import DhUserProfile
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.database.redis_client import get_redis_client, TaskState, TaskStep

# 导入原子化工具
from src.tools.travel_planner.format_tools_phase3 import format_llm_prompt_with_schema

# 创建专用的日志记录器，设置为DEBUG级别以显示详细信息
from src.core.logger import StructuredLogger
logger = StructuredLogger("travel_planner_agent", level="DEBUG", format="json")


class TravelPlannerAgent:
    """旅行规划Agent核心类"""
    
    def __init__(self):
        """初始化旅行规划Agent"""
        self.logger = logger
        self.settings = get_settings()
        self.llm_manager = LLMManager()
        self.map_tool = MapTool()  # 新增
        
        # 初始化LLM客户端
        self.reasoning_llm = self.llm_manager.get_client("reasoning")
        self.basic_llm = self.llm_manager.get_client("basic")
        
        # 初始化Redis客户端（延迟初始化）
        self._redis_client = None
        
    async def _get_redis_client(self):
        """获取Redis客户端实例"""
        if self._redis_client is None:
            self._redis_client = await get_redis_client()
        return self._redis_client
        
    async def _initialize_task_in_redis(self, request: TravelPlanRequest) -> TaskState:
        """在Redis中初始化任务状态"""
        redis_client = await self._get_redis_client()
        
        # 创建任务状态
        task_state = await redis_client.create_task_state(
            task_id=request.trace_id,
            user_id=request.user_id,
            original_query=request.query
        )
        
        # 更新状态为运行中
        await redis_client.update_task_state(
            request.trace_id,
            status="running",
            current_step="init"
        )
        
        self.logger.info(f"任务已在Redis中初始化: {request.trace_id}")
        return task_state
        
    async def _record_task_step(self, task_id: str, step_data: dict) -> TaskStep:
        """记录任务步骤到Redis"""
        redis_client = await self._get_redis_client()
        
        step = TaskStep(
            step_id=str(uuid.uuid4()),
            agent_name="TravelPlannerAgent",
            tool_name=step_data.get("tool_name"),
            tool_input=step_data.get("tool_input"),
            status=step_data.get("status", "completed"),
            input=step_data.get("input"),
            output=step_data.get("output"),
            timestamp=datetime.now(),
            business_step_log=step_data.get("business_step_log"),
            raw_model_trace=step_data.get("raw_model_trace")
        )
        
        await redis_client.record_task_step(task_id, step)
        return step
        
    async def _update_task_progress(self, task_id: str, progress: float, current_step: str):
        """更新任务进度"""
        redis_client = await self._get_redis_client()
        await redis_client.update_task_progress(task_id, progress, current_step)
        
    async def _store_l1_memory(self, task_id: str, memory_key: str, memory_data: Any):
        """存储L1短期记忆"""
        redis_client = await self._get_redis_client()
        await redis_client.store_l1_memory(task_id, memory_key, memory_data)
        
    async def _get_l1_memory(self, task_id: str, memory_key: str) -> Optional[Any]:
        """获取L1短期记忆"""
        redis_client = await self._get_redis_client()
        return await redis_client.get_l1_memory(task_id, memory_key)

    async def _restore_state_from_redis(self, trace_id: str) -> dict:
        """从Redis恢复状态"""
        try:
            # 尝试恢复各种状态数据
            state_data = {}

            # 恢复提取的实体
            extracted_entities = await self._get_l1_memory(trace_id, "extracted_entities")
            if extracted_entities:
                state_data["extracted_entities"] = extracted_entities

            # 恢复用户画像
            user_profile = await self._get_l1_memory(trace_id, "user_profile")
            if user_profile:
                state_data["user_profile"] = user_profile

            # 恢复用户记忆
            user_memories = await self._get_l1_memory(trace_id, "user_memories")
            if user_memories:
                state_data["user_memories"] = user_memories

            return state_data

        except Exception as e:
            self.logger.error(f"状态恢复失败: {e}")
            return {}

    def _get_user_tags(self, user_profile) -> list:
        """安全地获取用户标签，支持对象和字典两种格式"""
        if not user_profile:
            return []

        # 如果是对象，直接访问属性
        if hasattr(user_profile, 'tags'):
            return user_profile.tags or []

        # 如果是字典，访问键
        if isinstance(user_profile, dict):
            return user_profile.get('tags', [])

        return []
        
    async def _update_business_step_log(self, task_id: str, step_log: str):
        """更新业务步骤日志，兼容新Redis接口"""
        redis_client = await self._get_redis_client()
        # 简单规则映射
        if "开始" in step_log:
            step_id, status = "start", "init"
        elif "完成" in step_log:
            step_id, status = "end", "completed"
        elif "失败" in step_log:
            step_id, status = "error", "failed"
        else:
            step_id, status = "other", step_log
        await redis_client.update_business_step_log(task_id, step_id, status)
        
    async def _update_raw_model_trace(self, task_id: str, model_trace: str):
        """更新模型原始思考链，兼容新Redis接口"""
        redis_client = await self._get_redis_client()
        # 将字符串转换为字典格式
        trace_dict = {
            "input": model_trace,
            "output": "",
            "reasoning": ""
        }
        await redis_client.update_raw_model_trace(task_id, trace_dict)
        
    async def plan_travel(
        self,
        request: TravelPlanRequest,
        phase: str = "full"
    ) -> AsyncGenerator[StreamEvent, None]:
        """
        四阶段旅行规划工作流

        Args:
            request: 旅行规划请求
            phase: 执行阶段 ("analysis" - 仅分析阶段, "planning" - 仅规划阶段, "full" - 完整流程)

        Yields:
            StreamEvent: 流式事件
        """
        # 在Redis中初始化任务状态
        try:
            task_state = await self._initialize_task_in_redis(request)
        except Exception as e:
            self.logger.error(f"Redis任务初始化失败: {str(e)}")
            # 继续执行，不阻塞主流程
        
        # 初始化Agent状态
        state = AgentState(
            trace_id=request.trace_id,
            user_id=request.user_id,
            original_query=request.query,
            current_step="init"
        )

        # 如果是规划阶段，尝试从Redis恢复之前的状态
        if phase == "planning":
            import sys
            try:
                print(f"🔄 [状态恢复] 尝试从Redis恢复trace_id: {request.trace_id}的状态", file=sys.stderr, flush=True)
                saved_state = await self._restore_state_from_redis(request.trace_id)
                if saved_state:
                    # 恢复关键状态信息
                    state.extracted_entities = saved_state.get("extracted_entities", {})
                    state.user_profile = saved_state.get("user_profile")
                    state.user_memories = saved_state.get("user_memories")
                    print(f"✅ [状态恢复] 成功恢复状态，目的地: {state.extracted_entities.get('destination')}", file=sys.stderr, flush=True)
                else:
                    print(f"⚠️ [状态恢复] 未找到保存的状态，将重新执行意图理解", file=sys.stderr, flush=True)
            except Exception as e:
                print(f"❌ [状态恢复] 状态恢复失败: {e}", file=sys.stderr, flush=True)
                self.logger.error(f"状态恢复失败: {e}")
        
        try:
            # 更新任务进度到Redis
            await self._update_task_progress(request.trace_id, 0.0, "init")
            await self._update_business_step_log(request.trace_id, "开始旅行规划任务")
            
            # 根据phase参数决定执行哪些阶段
            if phase in ["analysis", "full"]:
                # Phase 1: 意图理解与个性化融合
                await self._update_task_progress(request.trace_id, 10.0, "phase1_intent_personalization")
                async for event in self._phase1_intent_and_personalization(state):
                    yield event

                # 如果只是分析阶段，执行完整的分析流程（A.1-A.4）
                if phase == "analysis":
                    # A.1: 景点偏好分析
                    await self._update_task_progress(request.trace_id, 33.0, "analysis_attraction_preferences")
                    async for event in self._analyze_attraction_preferences(state):
                        yield event

                    # A.2: 美食偏好分析
                    await self._update_task_progress(request.trace_id, 66.0, "analysis_food_preferences")
                    async for event in self._analyze_food_preferences(state):
                        yield event

                    # A.3: 住宿偏好分析
                    await self._update_task_progress(request.trace_id, 100.0, "analysis_accommodation_preferences")
                    async for event in self._analyze_accommodation_preferences(state):
                        yield event

                    # 发送分析完成事件
                    yield StreamEvent(
                        event_id=str(uuid.uuid4()),
                        trace_id=state.trace_id,
                        event_type=EventType.THINKING_STEP,
                        payload=ThinkingStepPayload(
                            category=ThinkingCategory.OTHER,
                            content="分析阶段完成，请点击'立即规划'开始生成行程"
                        ).model_dump()
                    )
                    return

            if phase in ["planning", "full"]:
                # Phase 2: 动态工具规划与并行执行
                await self._update_task_progress(request.trace_id, 30.0, "phase2_parallel_tools")
                async for event in self._phase2_parallel_tool_execution(state):
                    yield event

                # Phase 3: 数据综合与智能决策
                await self._update_task_progress(request.trace_id, 70.0, "phase3_reasoning")
                async for event in self._phase3_data_synthesis_and_reasoning(state):
                    yield event
                
            # Phase 4: 结构化结果生成与记忆存储
            await self._update_task_progress(request.trace_id, 90.0, "phase4_result_generation")
            async for event in self._phase4_result_generation_and_storage(state):
                yield event
                
            # 任务完成
            await self._update_task_progress(request.trace_id, 100.0, "completed")
            await self._update_business_step_log(request.trace_id, "旅行规划任务完成")
            
        except Exception as e:
            self.logger.error(f"旅行规划过程中发生错误: {str(e)}", extra={
                "trace_id": state.trace_id,
                "user_id": request.user_id,
                "error": str(e)
            })
            
            # 更新任务状态为失败
            try:
                await self._update_task_progress(request.trace_id, -1.0, "failed")
                await self._update_business_step_log(request.trace_id, f"任务失败: {str(e)}")
            except Exception as redis_error:
                self.logger.error(f"更新Redis失败状态时出错: {str(redis_error)}")
            
            # 发送错误事件
            error_event = StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.ERROR,
                payload={
                    "error_message": f"规划过程中发生错误: {str(e)}",
                    "error_type": type(e).__name__
                }
            )
            yield error_event
    
    async def _phase1_intent_and_personalization(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 1: 意图理解与个性化融合"""
        state.current_step = "phase1_intent_personalization"
        
        # 发送思考开始事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_START,
            payload={"phase": "Phase 1: 意图理解与个性化融合"}
        )
        
        # 步骤1: 意图理解和实体提取
        async for event in self._understand_intent(state):
            yield event
            
        # 步骤2: 获取用户画像
        async for event in self._get_user_profile(state):
            yield event
            
        # 步骤3: 构建个性化查询指令
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="已融合用户画像，准备开始动态工具规划"
            ).model_dump()
        )
    
    async def _phase2_parallel_tool_execution(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 2: 动态工具规划与四层并行执行"""
        state.current_step = "phase2_parallel_tools"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="开始四层并行工具调用，收集旅行规划所需信息"
            ).model_dump()
        )
        
        # 第一层：基础信息层
        async for event in self._layer1_basic_info(state):
            yield event
            
        # 第二层：核心POI信息层 
        async for event in self._layer2_core_poi_info(state):
            yield event
            
        # 第三层：路线与辅助信息层
        async for event in self._layer3_route_and_auxiliary(state):
            yield event
            
        # 第四层：深度信息挖掘层
        async for event in self._layer4_deep_mining(state):
            yield event
    
    async def _phase3_data_synthesis_and_reasoning(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 3: 数据综合与智能决策"""
        state.current_step = "phase3_reasoning"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="开始智能决策分析，综合所有信息生成最优行程方案"
            ).model_dump()
        )
        
        # 天气影响分析
        async for event in self._analyze_weather_impact(state):
            yield event
            
        # POI综合评分
        async for event in self._score_and_rank_pois(state):
            yield event
            
        # 智能行程编排
        async for event in self._orchestrate_itinerary(state):
            yield event
            
        # 预算分析
        async for event in self._estimate_budget(state):
            yield event
    
    async def _phase4_result_generation_and_storage(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """Phase 4: 结构化结果生成与记忆存储"""
        state.current_step = "phase4_generation_storage"
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在生成最终行程并保存用户记忆"
            ).model_dump()
        )

        # 生成最终行程
        async for event in self._generate_final_itinerary(state):
            yield event
            
        # 保存用户记忆 上一步的时候前端就停止接受，不执行后续了
        async for event in self._save_user_memory(state):
            yield event
            
    async def _understand_intent(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """使用原子化Function Call工具进行意图理解和实体提取"""
        state.current_step = "intent_understanding"
        
        # 发送思考开始事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_START,
            payload={"step": "意图理解和实体提取"}
        )
        
        # 获取当前时间
        current_time = datetime.now()
        current_date = current_time.strftime("%Y年%m月%d日")
        current_weekday = current_time.strftime("%A")
        weekday_cn = {
            "Monday": "周一", "Tuesday": "周二", "Wednesday": "周三", 
            "Thursday": "周四", "Friday": "周五", "Saturday": "周六", "Sunday": "周日"
        }
        current_weekday_cn = weekday_cn.get(current_weekday, current_weekday)
        current_datetime = f"{current_date} {current_weekday_cn}"
        
        # 构建基础提示词
        base_prompt = f"""
请分析用户的旅行规划需求，提取关键信息：

系统当前时间：{current_datetime}
用户查询：{state.original_query}
出行方式：开车（固定）

请严格按照JSON Schema提取以下信息：
1. 目的地城市（如果用户没有明确提及，请标记为null）
2. 出行天数（数字类型，如果没有明确提及则为null）
3. 出行时间（具体日期或相对时间，如"下周末"）
4. 出行人数和类型（如情侣、家庭、朋友等）
5. 预算范围（经济型/中等/豪华）
6. 兴趣偏好（如文化、美食、自然风光等）
7. 特殊需求（如无障碍、亲子友好等）

注意：天数字段必须是数字类型，出行方式固定为"driving"。
"""
        
        try:
            # 使用原子化工具生成包含Schema的完整提示词
            formatted_prompt = format_llm_prompt_with_schema(
                base_prompt=base_prompt,
                schema_tool_name="extract_travel_intent",
                context={"user_query": state.original_query, "current_datetime": current_datetime}
            )
            
            self.logger.info(f"[DEBUG] 使用Format Tool生成的提示词长度: {len(formatted_prompt)}")
            
            # 调用推理模型进行意图理解
            response = await self.reasoning_llm.chat_completion(
                messages=[{"role": "user", "content": formatted_prompt}],
                temperature=0.1
            )
            
            content = response['choices'][0]['message']['content']
            self.logger.info(f"[DEBUG] LLM响应内容: {content}")
            
            # 尝试提取JSON - 更严格的解析
            try:
                # 先尝试直接解析（假设LLM直接返回JSON）
                extracted_entities = json.loads(content.strip())
                self.logger.info(f"[DEBUG] 直接JSON解析成功: {extracted_entities}")
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON
                json_match = re.search(r'\{.*?\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    self.logger.info(f"[DEBUG] 从文本提取的JSON: {json_str}")
                    extracted_entities = json.loads(json_str)
                    self.logger.info(f"[DEBUG] 提取JSON解析成功: {extracted_entities}")
                else:
                    raise ValueError("LLM响应中未找到有效的JSON格式")
            
            # 验证和清理提取的实体数据
            extracted_entities = self._validate_extracted_entities(extracted_entities)
            state.extracted_entities = extracted_entities
            
            # 发送思考步骤事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.TRAVEL_OBJECT,
                    content=f"已提取用户需求：目的地={extracted_entities.get('destination')}, "
                           f"天数={extracted_entities.get('days')}, "
                           f"偏好={extracted_entities.get('preferences')}"
                ).model_dump()
            )
            
            self.logger.info(f"[SUCCESS] 意图理解完成，提取的实体: {extracted_entities}")

            # 保存提取的实体到Redis，供规划阶段使用
            try:
                await self._store_l1_memory(state.trace_id, "extracted_entities", extracted_entities)
                import sys
                print(f"💾 [状态保存] 已保存extracted_entities到Redis: {extracted_entities}", file=sys.stderr, flush=True)
            except Exception as save_error:
                self.logger.error(f"保存extracted_entities失败: {save_error}")

        except Exception as e:
            self.logger.error(f"[ERROR] 意图理解失败: {str(e)}", exc_info=True)
            
            # 设置默认值，确保后续处理不会出错
            default_entities = {
                "destination": None, 
                "origin": None,
                "days": 1,  # 默认1天，确保是整数
                "travel_time": None,
                "travelers": None,
                "budget": None,
                "preferences": None,
                "special_needs": None,
                "transport_mode": "driving"
            }
            state.extracted_entities = default_entities
            self.logger.info(f"[FALLBACK] 使用默认实体: {default_entities}")
            
            # 发送错误提示事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content=f"意图理解遇到问题，使用默认设置: {str(e)}"
                ).model_dump()
            )
    
    def _validate_extracted_entities(self, entities: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理提取的实体数据，兼容多种字段名格式"""
        validated = {}
        
        # 验证目的地 - 兼容多种字段名
        destination = (entities.get("destination") or 
                      entities.get("destination_city") or 
                      entities.get("目的地城市"))
        validated["destination"] = destination if destination != "" else None
            
        # 验证起点 - 兼容多种字段名
        origin = (entities.get("origin") or 
                 entities.get("出发点") or 
                 entities.get("起点"))
        validated["origin"] = origin if origin != "" else None
        
        # 验证天数（确保是整数） - 兼容多种字段名
        days = (entities.get("days") or 
               entities.get("travel_days") or  # 添加对travel_days字段的支持
               entities.get("出行天数") or 
               entities.get("天数"))
        if days is not None:
            try:
                validated["days"] = int(days)
            except (ValueError, TypeError):
                validated["days"] = 3  # 默认3天
        else:
            validated["days"] = 3
            
        # 验证出行时间 - 兼容多种字段名
        travel_time = (entities.get("travel_time") or 
                      entities.get("出行时间"))
        validated["travel_time"] = travel_time
        
        # 验证出行人员 - 兼容多种字段名
        travelers = (entities.get("travelers") or 
                    entities.get("出行人数和类型") or 
                    entities.get("出行人员"))
        validated["travelers"] = travelers
        
        # 验证预算 - 兼容多种字段名
        budget = (entities.get("budget") or 
                 entities.get("预算范围"))
        validated["budget"] = budget
        
        # 验证偏好 - 兼容多种字段名
        preferences = (entities.get("preferences") or 
                      entities.get("兴趣偏好") or 
                      entities.get("preferences_list"))
        validated["preferences"] = preferences if preferences is not None else []
        
        # 验证特殊需求 - 兼容多种字段名
        special_needs = (entities.get("special_needs") or 
                        entities.get("特殊需求"))
        validated["special_needs"] = special_needs if special_needs is not None else []
        
        # 验证出行方式 - 兼容多种字段名
        transport_mode = (entities.get("transport_mode") or 
                         entities.get("出行方式"))
        validated["transport_mode"] = transport_mode if transport_mode else "driving"
        
        self.logger.info(f"[DEBUG] 字段映射后的验证结果: {validated}")
        return validated

    def _extract_preferences_from_memories(self, memories: List) -> Dict[str, Any]:
        """从用户记忆中提取偏好信息"""
        preferences = {}
        
        # 统计记忆类型偏好
        memory_types = {}
        destinations = []
        activities = []
        
        for memory in memories:
            # 提取记忆类型
            if hasattr(memory, 'memory_type') and memory.memory_type:
                memory_types[memory.memory_type] = memory_types.get(memory.memory_type, 0) + 1
            
            # 提取内容中的偏好关键词
            if hasattr(memory, 'content') and memory.content:
                content = memory.content.lower()
                
                # 检测活动偏好
                if any(word in content for word in ['博物馆', '历史', '文化']):
                    activities.append('文化历史')
                if any(word in content for word in ['美食', '小吃', '餐厅']):
                    activities.append('美食')
                if any(word in content for word in ['自然', '风景', '山', '海']):
                    activities.append('自然风光')
                if any(word in content for word in ['购物', '商场', '买']):
                    activities.append('购物')
                if any(word in content for word in ['夜生活', '酒吧', '夜市']):
                    activities.append('夜生活')
                    
                # 检测预算偏好
                if any(word in content for word in ['经济', '便宜', '省钱']):
                    preferences['budget_style'] = '经济型'
                elif any(word in content for word in ['豪华', '高端', '奢华']):
                    preferences['budget_style'] = '豪华型'
                else:
                    preferences['budget_style'] = '中等'
        
        # 设置最喜欢的活动类型
        if activities:
            activity_count = {}
            for activity in activities:
                activity_count[activity] = activity_count.get(activity, 0) + 1
            preferences['favorite_activities'] = [k for k, v in sorted(activity_count.items(), key=lambda x: x[1], reverse=True)][:3]
        
        return preferences
    
    def _extract_preferences_from_itineraries(self, itineraries: List) -> Dict[str, Any]:
        """从历史行程中提取偏好信息"""
        preferences = {}
        
        destinations = []
        trip_durations = []
        
        for itinerary in itineraries:
            # 提取目的地偏好
            if hasattr(itinerary, 'city_name') and itinerary.city_name:
                destinations.append(itinerary.city_name)
            
            # 提取行程天数偏好
            if hasattr(itinerary, 'total_days') and itinerary.total_days:
                trip_durations.append(itinerary.total_days)
        
        # 设置偏好的目的地类型
        if destinations:
            preferences['preferred_destinations'] = list(set(destinations))[:5]
        
        # 设置偏好的行程天数
        if trip_durations:
            avg_duration = sum(trip_durations) / len(trip_durations)
            preferences['preferred_trip_duration'] = round(avg_duration)
        
        print("preferences", preferences)
        return preferences

    async def _get_user_profile(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取用户画像和记忆"""
        state.current_step = "get_user_profile"

        # 强制输出到控制台
        import sys
        print(f"🚀 [AGENT] 开始获取用户{state.user_id}的画像和记忆", file=sys.stderr, flush=True)
        print(f"🚀 [AGENT] trace_id: {state.trace_id}", file=sys.stderr, flush=True)
        print(f"🚀 [AGENT] 当前步骤: {state.current_step}", file=sys.stderr, flush=True)

        # 发送思考步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在解析用户需求和画像，分析出行目的地、时间、人员构成、兴趣偏好、预算和车辆数据"
            ).model_dump()
        )
        
        try:
            # 优先从数据库查询真实用户数据，而不是使用缓存
            self.logger.info(f"开始从数据库查询用户{state.user_id}的真实画像数据")

            # 发送数据库查询开始事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content="正在从数据库查询您的历史记忆和旅行偏好数据..."
                ).model_dump()
            )
            
            # 从数据库获取用户记忆和画像
            async with get_db() as db:
                print(f"🔍 [数据库查询] 开始查询用户{state.user_id}的数据库记录")
                self.logger.info(f"开始查询用户{state.user_id}的数据库记录")

                # 获取用户记忆
                print(f"📊 [数据库查询] 查询用户记忆表: dh_user_profile.user_memories")
                self.logger.info(f"查询用户记忆表: dh_user_profile.user_memories")
                user_memories = await user_memory_crud.get_by_user(db, user_id=state.user_id)
                print(f"✅ [数据库查询] 查询到{len(user_memories) if user_memories else 0}条用户记忆")
                self.logger.info(f"查询到{len(user_memories) if user_memories else 0}条用户记忆")

                # 获取用户画像总结
                print(f"📊 [数据库查询] 查询用户画像摘要表: dh_user_profile.user_summaries")
                self.logger.info(f"查询用户画像摘要表: dh_user_profile.user_summaries")
                user_summary = await user_summary_crud.get_by_user(db, user_id=state.user_id)
                print(f"✅ [数据库查询] 用户画像摘要查询结果: {'找到' if user_summary else '未找到'}")
                self.logger.info(f"用户画像摘要查询结果: {'找到' if user_summary else '未找到'}")

                # 获取历史行程
                print(f"📊 [数据库查询] 查询历史行程表: dh_tripplanner.itineraries")
                self.logger.info(f"查询历史行程表: dh_tripplanner.itineraries")
                itineraries = await itinerary_crud.get_by_user(db, user_id=state.user_id)
                print(f"✅ [数据库查询] 查询到{len(itineraries) if itineraries else 0}条历史行程")
                self.logger.info(f"查询到{len(itineraries) if itineraries else 0}条历史行程")

                # 获取用户旅行画像（从dh_tripplanner数据库）
                user_travel_profile = None
                try:
                    from src.database.mysql_client import fetch_one
                    print(f"📊 [数据库查询] 查询用户旅行偏好表: dh_tripplanner.user_travel_profiles")
                    self.logger.info(f"查询用户旅行偏好表: dh_tripplanner.user_travel_profiles")
                    # 查询用户旅行画像
                    travel_profile_query = """
                    SELECT user_id, travel_style, accommodation_pref, transportation_pref,
                           travel_summary, travel_keywords, updated_at
                    FROM dh_tripplanner.user_travel_profiles
                    WHERE user_id = %s
                    """
                    print(f"🔍 [SQL查询] {travel_profile_query.strip()} 参数: user_id={state.user_id}")
                    user_travel_profile = await fetch_one(travel_profile_query, (state.user_id,))
                    print(f"✅ [数据库查询] 用户旅行偏好查询结果: {'找到' if user_travel_profile else '未找到'}")
                    self.logger.info(f"用户旅行偏好查询结果: {'找到' if user_travel_profile else '未找到'}")
                    if user_travel_profile:
                        print(f"📋 [查询结果] 旅行风格: {user_travel_profile.get('travel_style')}")
                        print(f"📋 [查询结果] 旅行总结: {user_travel_profile.get('travel_summary', '')[:100]}...")
                        self.logger.info(f"旅行风格: {user_travel_profile.get('travel_style')}")
                        self.logger.info(f"旅行总结: {user_travel_profile.get('travel_summary', '')[:100]}...")
                except Exception as e:
                    print(f"❌ [数据库查询] 获取用户旅行画像失败: {e}")
                    self.logger.error(f"获取用户旅行画像失败: {e}")
                    user_travel_profile = None
                
                # 构建完整的用户画像
                preferences = {}
                tags = []

                # 从用户旅行画像中提取偏好（优先级最高）
                if user_travel_profile:
                    preferences['travel_style'] = user_travel_profile.get('travel_style', '休闲')

                    # 处理住宿偏好
                    accommodation_pref = user_travel_profile.get('accommodation_pref')
                    if accommodation_pref:
                        if isinstance(accommodation_pref, str):
                            import json
                            try:
                                accommodation_pref = json.loads(accommodation_pref)
                            except:
                                pass
                        if isinstance(accommodation_pref, list):
                            preferences['accommodation_preferences'] = accommodation_pref

                    # 处理交通偏好
                    transportation_pref = user_travel_profile.get('transportation_pref')
                    if transportation_pref:
                        if isinstance(transportation_pref, str):
                            import json
                            try:
                                transportation_pref = json.loads(transportation_pref)
                            except:
                                pass
                        if isinstance(transportation_pref, list):
                            preferences['transportation_preferences'] = transportation_pref

                    # 处理旅行关键词
                    travel_keywords = user_travel_profile.get('travel_keywords')
                    if travel_keywords:
                        if isinstance(travel_keywords, str):
                            import json
                            try:
                                travel_keywords = json.loads(travel_keywords)
                            except:
                                travel_keywords = [travel_keywords]
                        if isinstance(travel_keywords, list):
                            tags.extend(travel_keywords)

                # 从用户记忆中提取偏好
                if user_memories:
                    memory_preferences = self._extract_preferences_from_memories(user_memories)
                    preferences.update(memory_preferences)

                    # 从记忆关键词中提取标签
                    for memory in user_memories:
                        if hasattr(memory, 'keywords') and memory.keywords:
                            if isinstance(memory.keywords, list):
                                tags.extend(memory.keywords)
                            elif isinstance(memory.keywords, str):
                                import json
                                try:
                                    keywords = json.loads(memory.keywords)
                                    if isinstance(keywords, list):
                                        tags.extend(keywords)
                                except:
                                    tags.append(memory.keywords)
                
                # 从用户画像总结中获取信息
                if user_summary:
                    if hasattr(user_summary, 'keywords') and user_summary.keywords:
                        if isinstance(user_summary.keywords, list):
                            tags.extend(user_summary.keywords)
                    
                    if hasattr(user_summary, 'travel_style') and user_summary.travel_style:
                        preferences['travel_style'] = user_summary.travel_style
                        
                    if hasattr(user_summary, 'budget_preference') and user_summary.budget_preference:
                        preferences['budget_preference'] = user_summary.budget_preference
                
                # 从历史行程中提取偏好
                if itineraries:
                    itinerary_preferences = self._extract_preferences_from_itineraries(itineraries)
                    preferences.update(itinerary_preferences)
                
                # 创建用户画像
                user_profile = UserProfile(
                    user_id=state.user_id,
                    preferences=preferences,
                    tags=list(set(tags))[:10],  # 去重并限制数量
                    travel_style=preferences.get('travel_style', '休闲'),
                    budget_preference=preferences.get('budget_preference', '中等'),
                    favorite_activities=preferences.get('favorite_activities', []),
                    preferred_destinations=preferences.get('preferred_destinations', []),
                    preferred_trip_duration=preferences.get('preferred_trip_duration', 3)
                )
                
                state.user_profile = user_profile
                state.user_memories = user_memories
                
                # 将用户画像存储到Redis L1记忆中
                await self._store_l1_memory(
                    state.trace_id, 
                    "user_profile", 
                    user_profile.model_dump()
                )
                
                # 将用户记忆存储到Redis L1记忆中
                if user_memories:
                    # 将SQLAlchemy对象转换为可序列化的字典
                    serializable_memories = []
                    for memory in user_memories:
                        memory_dict = {}
                        for key, value in memory.__dict__.items():
                            # 跳过SQLAlchemy内部属性
                            if not key.startswith('_'):
                                # 处理datetime对象
                                if hasattr(value, 'isoformat'):
                                    memory_dict[key] = value.isoformat()
                                # 处理其他可序列化的对象
                                elif isinstance(value, (str, int, float, bool, list, dict, type(None))):
                                    memory_dict[key] = value
                                else:
                                    # 对于其他对象，转换为字符串
                                    memory_dict[key] = str(value)
                        serializable_memories.append(memory_dict)
                    
                    await self._store_l1_memory(
                        state.trace_id, 
                        "user_memories", 
                        serializable_memories
                    )
                
                # 记录任务步骤
                await self._record_task_step(state.trace_id, {
                    "tool_name": "user_profile_retrieval",
                    "status": "completed",
                    "input": f"user_id: {state.user_id}",
                    "output": f"获取到用户画像，偏好数量: {len(preferences)}, 标签数量: {len(tags)}",
                    "business_step_log": f"成功获取用户{state.user_id}的画像和历史记忆"
                })
                
                # 生成基于真实数据的用户画像分析
                self.logger.info(f"开始基于真实数据生成用户{state.user_id}的画像分析")
                profile_analysis = []

                # 记录数据来源
                data_sources = []
                if user_travel_profile:
                    data_sources.append("旅行偏好数据")
                if user_memories:
                    data_sources.append(f"{len(user_memories)}条历史记忆")
                if user_summary:
                    data_sources.append("用户画像摘要")
                if itineraries:
                    data_sources.append(f"{len(itineraries)}条历史行程")

                self.logger.info(f"数据来源: {', '.join(data_sources) if data_sources else '无真实数据，使用默认设置'}")

                # 分析旅行风格
                travel_style = preferences.get('travel_style', '休闲')
                self.logger.info(f"分析旅行风格: {travel_style}")
                if travel_style == 'ADVENTURE':
                    profile_analysis.append("您偏爱探索人迹罕至的自然风光和有历史感的目的地")
                elif travel_style == 'RELAXED':
                    profile_analysis.append("您极度重视行程的舒适度和安全性")
                elif travel_style == 'FAMILY':
                    profile_analysis.append("您的旅行画像核心是'新奇有趣'，适合家庭出游")
                else:
                    profile_analysis.append(f"您的旅行风格偏向：{travel_style}")

                # 分析住宿偏好
                accommodation_prefs = preferences.get('accommodation_preferences', [])
                if accommodation_prefs:
                    self.logger.info(f"住宿偏好: {accommodation_prefs}")
                    profile_analysis.append(f"住宿偏好：{', '.join(accommodation_prefs)}")

                # 分析交通偏好
                transportation_prefs = preferences.get('transportation_preferences', [])
                if transportation_prefs:
                    self.logger.info(f"交通偏好: {transportation_prefs}")
                    profile_analysis.append(f"交通偏好：{', '.join(transportation_prefs)}")

                # 分析旅行关键词
                if tags:
                    self.logger.info(f"兴趣标签: {tags[:5]}")
                    profile_analysis.append(f"您的兴趣标签包括：{', '.join(tags[:5])}")

                # 分析旅行总结
                if user_travel_profile and user_travel_profile.get('travel_summary'):
                    travel_summary = user_travel_profile.get('travel_summary')
                    self.logger.info(f"个人旅行特色: {travel_summary[:100]}...")
                    profile_analysis.append(f"个人旅行特色：{travel_summary}")

                if not profile_analysis:
                    self.logger.warning(f"用户{state.user_id}没有找到任何真实数据，使用默认分析")
                    profile_analysis = ["这是您的首次使用，我将为您提供通用的旅行建议"]

                content = "解析用户需求和画像完成。" + "；".join(profile_analysis) + "。"
                self.logger.info(f"用户画像分析完成: {content[:200]}...")

                # 发送用户画像分析事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.OTHER,
                        content=content
                    ).model_dump()
                )
                
        except Exception as e:
            self.logger.error(f"数据库查询用户画像失败: {str(e)}", exc_info=True)

            # 数据库查询失败时，尝试从Redis缓存获取作为备选方案
            try:
                self.logger.info(f"数据库查询失败，尝试从Redis缓存获取用户{state.user_id}的画像数据")
                cached_profile = await self._get_l1_memory(state.trace_id, "user_profile")
                if cached_profile:
                    self.logger.info(f"从Redis缓存成功获取用户画像")
                    state.user_profile = UserProfile(**cached_profile)

                    # 发送缓存获取事件
                    yield StreamEvent(
                        event_id=str(uuid.uuid4()),
                        trace_id=state.trace_id,
                        event_type=EventType.THINKING_STEP,
                        payload=ThinkingStepPayload(
                            category=ThinkingCategory.OTHER,
                            content="数据库查询失败，已从缓存获取用户画像数据"
                        ).model_dump()
                    )
                    return
                else:
                    self.logger.warning(f"Redis缓存中也没有找到用户{state.user_id}的画像数据")
            except Exception as cache_error:
                self.logger.error(f"从缓存获取用户画像也失败: {cache_error}")

            # 记录错误步骤
            await self._record_task_step(state.trace_id, {
                "tool_name": "user_profile_retrieval",
                "status": "failed",
                "input": f"user_id: {state.user_id}",
                "output": f"数据库和缓存都失败: {str(e)}",
                "business_step_log": f"获取用户画像失败，使用默认设置: {str(e)}"
            })

            # 最后创建默认用户画像
            self.logger.info(f"为用户{state.user_id}创建默认画像")
            state.user_profile = UserProfile(
                user_id=state.user_id,
                preferences={},
                tags=[],
                travel_style="休闲",
                budget_preference="中等"
            )

            # 发送默认画像事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content="解析用户需求和画像完成。这是您的首次使用，我将为您提供通用的旅行建议。"
                ).model_dump()
            )
    
    async def _geolocate_destination(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """地理定位"""
        state.current_step = "geolocation"
        self.logger.info(f"[DEBUG] 开始地理定位")
        
        destination = state.extracted_entities.get("destination")
        self.logger.info(f"[DEBUG] 提取的目的地: {destination}")
        if not destination:
            self.logger.info(f"[DEBUG] 目的地为空，跳过地理定位")
            return
            
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_geo",
                parameters={"address": destination}
            ).model_dump()
        )
        
        try:
            # 使用同步map_tool方法替换amap_client.maps_geo
            geo_result = self.map_tool.geocode_address(address=destination)
            state.tool_results["geolocation"] = geo_result
            
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"成功定位{destination}",
                    success=True
                ).model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"地理定位失败: {str(e)}")
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"定位失败: {str(e)}",
                    success=False
                ).model_dump()
            )
            
    async def _get_weather_forecast(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取天气预报（使用map_tool.get_weather_info替换原有amap_client.maps_weather）"""
        state.current_step = "weather_forecast"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
        
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_weather",
                parameters={"city": destination}
            ).model_dump()
        )
        
        try:
            # 使用同步map_tool方法（假设city为城市名或adcode）
            weather_result = self.map_tool.get_weather_info(city=destination)
            state.tool_results["weather"] = weather_result
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_weather",
                    result_summary=f"获取{destination}天气预报成功",
                    success=True
                ).model_dump()
            )
        except Exception as e:
            self.logger.error(f"天气查询失败: {str(e)}")
            
    async def _search_and_recommend_pois(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """搜索和推荐POI"""
        state.current_step = "poi_search"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
        
        # 根据用户偏好搜索不同类型的POI
        poi_categories = ["景点", "美食", "酒店"]
        
        for category in poi_categories:
            # 发送工具调用事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_CALL,
                payload=ToolCallPayload(
                    tool_name="maps_text_search",
                    parameters={"keywords": category, "city": destination}
                ).model_dump()
            )
            
            try:
                # 使用map_tool同步方法
                search_result = self.map_tool.search_pois(
                    keywords=category,
                    city=destination,
                    page_size=10
                )
                # 转为dict列表
                search_result_dicts = [poi.__dict__ for poi in search_result]
                state.tool_results[f"poi_{category}"] = {"pois": search_result_dicts}
                # 发送工具结果事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.TOOL_RESULT,
                    payload=ToolResultPayload(
                        tool_name="maps_text_search",
                        result_summary=f"找到{len(search_result_dicts)}个{category}POI",
                        success=True
                    ).model_dump()
                )
                # 对前3个POI获取详细信息
                pois = search_result_dicts[:3]
                for poi in pois:
                    poi_id = poi.get('id')
                    if poi_id:
                        try:
                            detail_result = self.map_tool.place_detail_by_id(id=poi_id)
                            state.tool_results[f"poi_detail_{poi_id}"] = detail_result
                        except Exception as e:
                            self.logger.error(f"获取POI详情失败 {poi_id}: {str(e)}")
                
            except Exception as e:
                self.logger.error(f"POI搜索失败 ({category}): {str(e)}")

    async def _plan_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """行程规划"""
        state.current_step = "itinerary_planning"
        
        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划每日行程安排"}
        )
        
        # 这里实现具体的行程规划逻辑
        # 暂时创建示例行程
        days = state.extracted_entities.get("days", 3)
        
        # 确保days是整数类型，防止None + int错误
        if days is None or not isinstance(days, int):
            days = 3
            
        daily_plans = []
        
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=[]
            )
            daily_plans.append(daily_plan)
            
        state.tool_results["daily_plans"] = daily_plans
        
    async def _plan_routes(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """路线规划"""
        state.current_step = "route_planning"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划最优路线"}
        )

        # 获取每日行程中的POI，规划路线
        daily_plans = state.tool_results.get("daily_plans", [])

        for daily_plan in daily_plans:
            if len(daily_plan.pois) > 1:
                pois = daily_plan.pois
                for i in range(len(pois) - 1):
                    origin_poi = pois[i]
                    dest_poi = pois[i + 1]
                    if origin_poi.location and dest_poi.location:
                        try:
                            # 使用map_tool同步方法
                            route_result = self.map_tool.get_route(
                                origin=origin_poi.location,
                                destination=dest_poi.location,
                                transport_mode="walking"
                            )
                            route_key = f"route_day_{daily_plan.day}_{i}"
                            state.tool_results[route_key] = route_result
                        except Exception as e:
                            self.logger.error(f"路线规划失败: {str(e)}")

        # 生成个人地图链接
        try:
            all_pois = []
            for daily_plan in daily_plans:
                for poi in daily_plan.pois:
                    if poi.location:
                        all_pois.append(f"{poi.location.longitude},{poi.location.latitude}")

            if all_pois:
                amap_client = await get_amap_client()
                pois_str = "|".join(all_pois)
                map_result = await amap_client.maps_schema_personal_map(
                    pois=pois_str,
                    name=f"{state.extracted_entities.get('destination', '旅行')}行程地图"
                )
                state.tool_results["personal_map"] = map_result

        except Exception as e:
            self.logger.error(f"生成个人地图失败: {str(e)}")

    async def _estimate_budget(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """预算估算"""
        state.current_step = "budget_estimation"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在估算旅行预算"}
        )

        # 基于POI类型和数量估算预算
        days = state.extracted_entities.get("days", 3)
        
        # 确保days是整数类型，防止None * int错误
        if days is None or not isinstance(days, int):
            days = 3
            
        daily_plans = state.tool_results.get("daily_plans", [])

        # 预算估算逻辑
        accommodation_cost = days * 200  # 每晚住宿200元
        food_cost = days * 150  # 每天餐饮150元
        attraction_cost = 0
        transport_cost = days * 50  # 每天交通50元

        # 根据景点数量估算门票费用
        for daily_plan in daily_plans:
            for poi in daily_plan.pois:
                if poi.category == "景点":
                    attraction_cost += 50  # 平均门票50元

        breakdown = [
            BudgetBreakdown(category="住宿", amount=accommodation_cost),
            BudgetBreakdown(category="餐饮", amount=food_cost),
            BudgetBreakdown(category="景点门票", amount=attraction_cost),
            BudgetBreakdown(category="交通", amount=transport_cost)
        ]

        total_cost = accommodation_cost + food_cost + attraction_cost + transport_cost

        budget_estimation = BudgetEstimation(
            total_min=total_cost * 0.8,
            total_max=total_cost * 1.2,
            breakdown=breakdown
        )

        state.tool_results["budget"] = budget_estimation
        
    async def _generate_final_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """使用原子化Function Call工具生成最终行程"""
        state.current_step = "final_generation"
        
        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在生成最终行程JSON"}
        )
        
        # 验证并获取必需字段
        destination = state.extracted_entities.get("destination")
        days = state.extracted_entities.get("days", 3)
        
        # 如果目的地为空，使用默认值或从工具结果中推断
        if not destination:
            destination = self._infer_destination_from_results(state)
            
        # 确保days是有效的整数
        if not isinstance(days, int) or days <= 0:
            days = 3
            
        self.logger.info(f"[DEBUG] 生成最终行程 - 目的地: {destination}, 天数: {days}")
        
        try:
            # 构建完整的行程数据，符合Schema要求
            summary = TripSummary(
                title=f"{destination}{days}日游",
                days=days,
                destination_city=destination,
                tags=["智能规划", "开车出行"],
                description="AI智能规划的旅行行程"
            )
            
            # 确保daily_plans有正确的数据结构
            daily_plans = self._build_valid_daily_plans(state, days)
            
            final_itinerary = TravelItinerary(
                trace_id=state.trace_id,
                user_id=state.user_id,
                status="completed",
                raw_user_query=state.original_query,
                summary=summary,
                weather_forecast=state.tool_results.get("weather_forecast", []),
                daily_plans=daily_plans,
                budget_estimation=state.tool_results.get("budget")
            )
            
            # 验证最终行程是否符合Schema
            self._validate_final_itinerary(final_itinerary)
            
            state.final_itinerary = final_itinerary

            # 发送最终行程事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.FINAL_ITINERARY,
                payload=final_itinerary.model_dump()
            )
            
            self.logger.info(f"[SUCCESS] 最终行程生成完成，trace_id: {state.trace_id}")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 生成最终行程失败: {str(e)}", exc_info=True)
            
            # 生成最小化的有效行程
            fallback_itinerary = self._create_fallback_itinerary(state, destination, days)
            state.final_itinerary = fallback_itinerary
            
            # 发送最终行程事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.FINAL_ITINERARY,
                payload=fallback_itinerary.model_dump()
            )
    
    def _infer_destination_from_results(self, state: AgentState) -> str:
        """从工具结果中推断目的地"""
        # 尝试从地理定位结果中获取
        geo_result = state.tool_results.get("geolocation")
        if geo_result and isinstance(geo_result, dict):
            # 从高德地理编码结果中提取城市名
            if "geocodes" in geo_result and len(geo_result["geocodes"]) > 0:
                return geo_result["geocodes"][0].get("city", "未知目的地")
        
        # 从用户查询中尝试提取地名
        query = state.original_query
        # 简单的地名提取逻辑
        import re
        city_pattern = r'([北京|上海|广州|深圳|杭州|南京|苏州|成都|重庆|西安|天津|青岛|大连|厦门|武汉|长沙|沈阳|哈尔滨|济南|昆明|贵阳|兰州|银川|西宁|拉萨|乌鲁木齐|呼和浩特|石家庄|太原|合肥|福州|南昌|郑州|长春|海口|三亚|桂林|丽江|九寨沟|黄山|泰山|华山|峨眉山|庐山|衡山|嵩山|恒山|五台山]|.*市|.*县|.*区)'
        match = re.search(city_pattern, query)
        if match:
            return match.group(1)
            
        return "目的地"
    
    def _build_valid_daily_plans(self, state: AgentState, days: int) -> List[DailyPlan]:
        """构建有效的每日行程计划"""
        existing_plans = state.tool_results.get("daily_plans", [])
        
        # 如果已有计划且数量正确，直接使用
        if existing_plans and len(existing_plans) == days:
            return existing_plans
            
        # 否则构建新的计划
        daily_plans = []
        for day in range(1, days + 1):
            # 从现有计划中获取对应天的数据，或创建默认计划
            existing_plan = None
            if existing_plans and len(existing_plans) >= day:
                existing_plan = existing_plans[day - 1]
                
            if existing_plan and hasattr(existing_plan, 'pois'):
                daily_plan = existing_plan
            else:
                # 创建默认的每日计划
                daily_plan = DailyPlan(
                    day=day,
                    theme=f"第{day}天行程",
                    pois=self._create_sample_pois(state, day)
                )
            
            daily_plans.append(daily_plan)
            
        return daily_plans
    
    def _create_sample_pois(self, state: AgentState, day: int) -> List[POIInfo]:
        """为指定天创建示例POI"""
        destination = state.extracted_entities.get("destination", "目的地")
        
        # 基于天数创建不同的POI
        sample_pois = []
        
        if day == 1:
            # 第一天：主要景点
            sample_pois.append(POIInfo(
                poi_instance_id=f"poi_{day}_1",
                name=f"{destination}标志性景点",
                category="景点",
                description="当地著名景点",
                estimated_duration_min=120
            ))
        else:
            # 其他天：其他类型POI
            sample_pois.append(POIInfo(
                poi_instance_id=f"poi_{day}_1",
                name=f"{destination}特色体验",
                category="景点",
                description="当地特色活动",
                estimated_duration_min=90
            ))
            
        return sample_pois
    
    def _validate_final_itinerary(self, itinerary: TravelItinerary) -> None:
        """验证最终行程是否符合Schema要求"""
        # 检查必需字段
        if not itinerary.trace_id:
            raise ValueError("trace_id不能为空")
        if not itinerary.user_id:
            raise ValueError("user_id不能为空")
        if not itinerary.summary.title:
            raise ValueError("行程标题不能为空")
        if not itinerary.summary.destination_city:
            raise ValueError("目的地城市不能为空")
        if itinerary.summary.days <= 0:
            raise ValueError("天数必须大于0")
            
        # 检查每日计划
        if not itinerary.daily_plans:
            raise ValueError("必须包含每日计划")
        
        for daily_plan in itinerary.daily_plans:
            if not isinstance(daily_plan.day, int) or daily_plan.day <= 0:
                raise ValueError(f"每日计划的天数必须是正整数: {daily_plan.day}")
                
        self.logger.info("[VALIDATION] 最终行程验证通过")
    
    def _create_fallback_itinerary(self, state: AgentState, destination: str, days: int) -> TravelItinerary:
        """创建最小化的有效行程作为后备方案"""
        summary = TripSummary(
            title=f"{destination}{days}日游",
            days=days,
            destination_city=destination,
            tags=["智能规划"],
            description="AI智能规划的旅行行程（简化版）"
        )
        
        # 创建最基本的每日计划
        daily_plans = []
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天",
                pois=[POIInfo(
                    poi_instance_id=f"fallback_poi_{day}",
                    name=f"{destination}游览",
                    category="景点"
                )]
            )
            daily_plans.append(daily_plan)
        
        return TravelItinerary(
            trace_id=state.trace_id,
            user_id=state.user_id,
            status="completed",
            raw_user_query=state.original_query,
            summary=summary,
            weather_forecast=[],
            daily_plans=daily_plans
        )

    async def _layer1_basic_info(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第一层并行调用：基础信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第一层：并行获取地理定位和天气信息"
            ).model_dump()
        )
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            self.logger.warning("目的地为空，跳过地理定位")
            return
            
        # 并行调用地理定位和天气查询
        tasks = []
        
        # 地理定位任务
        async def geolocate_task():
            async for event in self._geolocate_destination(state):
                yield event
                
        # 天气查询任务  
        async def weather_task():
            async for event in self._get_weather_forecast(state):
                yield event
        
        # 并行执行地理定位和天气查询
        import asyncio
        geolocate_gen = geolocate_task()
        weather_gen = weather_task()
        
        try:
            # 并行执行两个生成器
            async for event in geolocate_gen:
                yield event
            async for event in weather_gen:
                yield event
        except Exception as e:
            self.logger.error(f"第一层并行调用失败: {str(e)}")

    async def _layer2_core_poi_info(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第二层并行调用：核心POI信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                content="第二层：并行搜索停车场、美食、景点、住宿等核心POI"
            ).model_dump()
        )
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 定义POI搜索类别和关键词
        poi_searches = [
            # ("停车场", "停车场"),
            # ("充电桩", "充电桩"), 
            ("美食", "美食"),
            ("景点", "景点"),
            ("酒店", "酒店")
        ]
        
        # 并行执行POI搜索
        import asyncio
        # amap_client = await get_amap_client()  # 删除
        
        for category, keywords in poi_searches:
            # 发送工具调用事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_CALL,
                payload=ToolCallPayload(
                    tool_name="maps_text_search",
                    parameters={"keywords": keywords, "city": destination}
                ).model_dump()
            )
            
            try:
                # 使用map_tool同步方法
                search_result = self.map_tool.search_pois(
                    keywords=keywords,
                    city=destination,
                    page_size=20
                )
                # 转为dict列表
                search_result_dicts = [poi.__dict__ for poi in search_result]
                state.tool_results[f"poi_{category}"] = {"pois": search_result_dicts}
                # 发送工具结果事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.TOOL_RESULT,
                    payload=ToolResultPayload(
                        tool_name="maps_text_search",
                        result_summary=f"找到{len(search_result_dicts)}个{category}POI",
                        success=True
                    ).model_dump()
                )
                # 对前3个POI获取详细信息
                pois = search_result_dicts[:3]
                for poi in pois:
                    poi_id = poi.get('id')
                    if poi_id:
                        try:
                            # 这里如需详情可扩展map_tool
                            pass
                        except Exception as e:
                            self.logger.error(f"获取POI详情失败 {poi_id}: {str(e)}")
            
            except Exception as e:
                self.logger.error(f"POI搜索失败 ({category}): {str(e)}")
                
    async def _layer3_route_and_auxiliary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第三层并行调用：路线与辅助信息层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第三层：规划路线和收集辅助信息"
            ).model_dump()
        )
        
        # 基于前面收集的POI信息进行路线规划
        # 这里可以实现具体的路线规划逻辑
        pass
        
    async def _layer4_deep_mining(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """第四层并行调用：深度信息挖掘层"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="第四层：个性化深度挖掘和应急信息收集"
            ).model_dump()
        )
        
        # 基于用户画像进行个性化搜索
        # 收集应急信息(医院、药店等)
        pass
        
    async def _analyze_weather_impact(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析天气对行程的影响"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="分析天气影响，调整室内外活动安排"
            ).model_dump()
        )
        
        weather_data = state.tool_results.get("weather")
        if weather_data:
            # 基于天气数据调整推荐策略
            # 如有雨天则推荐室内活动
            state.weather_analysis = {
                "has_rain": False,  # 实际应该解析天气数据
                "indoor_recommended": False,
                "suggestions": []
            }
            
    async def _score_and_rank_pois(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """POI综合评分和排序"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                content="正在对POI进行综合评分和智能排序"
            ).model_dump()
        )
        # 实现POI评分算法
        # Score = w1 * 评分 + w2 * (1/距离) + w3 * 用户偏好匹配度
        
        # 收集所有POI并评分
        all_pois = []
        for key, value in state.tool_results.items():
            if key.startswith("poi_") and isinstance(value, dict):
                pois = value.get('pois', [])
                for poi in pois:
                    poi['category'] = key.replace('poi_', '')
                    poi['score'] = self._calculate_poi_score(poi, state)
                    all_pois.append(poi)
                    
        # 按分数排序
        all_pois.sort(key=lambda x: x.get('score', 0), reverse=True)
        state.scored_pois = all_pois
        
    def _calculate_poi_score(self, poi: dict, state: AgentState) -> float:
        """基于用户画像和记忆计算POI综合得分"""
        score = 0.0
        
        # 基础评分权重 (30%)
        rating = float(poi.get('rating', 0))
        score += rating * 0.3
        
        # 用户画像偏好匹配 (40%)
        if hasattr(state, 'user_profile') and state.user_profile:
            profile_score = self._calculate_profile_match_score(poi, state.user_profile)
            score += profile_score * 0.4
        
        # 用户记忆匹配 (20%)
        if hasattr(state, 'user_memories') and state.user_memories:
            memory_score = self._calculate_memory_match_score(poi, state.user_memories)
            score += memory_score * 0.2
        
        # 类别权重调整 (10%)
        category = poi.get('category', '')
        if category == '景点':
            score += 0.5
        elif category == '美食':
            score += 0.3
        elif category == '酒店':
            score += 0.2
        elif category == '停车场':
            score += 0.1  # 必需但不是主要兴趣点
            
        return score
    
    def _calculate_profile_match_score(self, poi: dict, user_profile: UserProfile) -> float:
        """计算POI与用户画像的匹配分数"""
        match_score = 0.0
        
        poi_name = poi.get('name', '').lower()
        poi_type = poi.get('type', '').lower()
        poi_category = poi.get('category', '').lower()
        
        # 检查用户标签匹配
        user_tags = self._get_user_tags(user_profile)
        for tag in user_tags:
            tag_lower = tag.lower()
            if tag_lower in poi_name or tag_lower in poi_type:
                match_score += 1.0
        
        # 检查偏好活动匹配
        # 安全地获取偏好活动，支持对象和字典两种格式
        if hasattr(user_profile, 'preferences'):
            favorite_activities = user_profile.preferences.get('favorite_activities', [])
        elif isinstance(user_profile, dict):
            preferences = user_profile.get('preferences', {})
            favorite_activities = preferences.get('favorite_activities', []) if isinstance(preferences, dict) else []
        else:
            favorite_activities = []
        for activity in favorite_activities:
            activity_lower = activity.lower()
            if '文化历史' in activity_lower and any(word in poi_name for word in ['博物馆', '历史', '文化', '古迹']):
                match_score += 2.0
            elif '美食' in activity_lower and any(word in poi_name for word in ['餐厅', '美食', '小吃', '菜']):
                match_score += 2.0
            elif '自然风光' in activity_lower and any(word in poi_name for word in ['公园', '山', '湖', '海', '风景']):
                match_score += 2.0
            elif '购物' in activity_lower and any(word in poi_name for word in ['商场', '市场', '购物']):
                match_score += 2.0
        
        # 检查预算偏好匹配
        # 安全地获取预算偏好，支持对象和字典两种格式
        if hasattr(user_profile, 'budget_preference'):
            budget_pref = user_profile.budget_preference
        elif isinstance(user_profile, dict):
            budget_pref = user_profile.get('budget_preference', '中等')
        else:
            budget_pref = '中等'
        if budget_pref == '经济型':
            # 经济型用户偏好评分相对较低但性价比高的地方
            if rating := poi.get('rating'):
                if 3.5 <= float(rating) <= 4.2:
                    match_score += 0.5
        elif budget_pref == '豪华型':
            # 豪华型用户偏好高评分场所
            if rating := poi.get('rating'):
                if float(rating) >= 4.5:
                    match_score += 1.0
        
        return min(match_score, 5.0)  # 限制最高分数
    
    def _calculate_memory_match_score(self, poi: dict, user_memories: List) -> float:
        """计算POI与用户记忆的匹配分数"""
        match_score = 0.0
        
        poi_name = poi.get('name', '').lower()
        poi_type = poi.get('type', '').lower()
        
        for memory in user_memories[:10]:  # 只检查最近的10条记忆
            if hasattr(memory, 'content') and memory.content:
                memory_content = memory.content.lower()
                
                # 检查记忆内容与POI的相似性
                common_keywords = self._extract_common_keywords(memory_content, poi_name + ' ' + poi_type)
                if common_keywords:
                    # 根据记忆置信度加权
                    confidence = getattr(memory, 'confidence_score', 0.5)
                    match_score += len(common_keywords) * confidence * 0.5
                
                # 检查记忆关键词匹配
                if hasattr(memory, 'keywords') and memory.keywords:
                    keywords = memory.keywords
                    if isinstance(keywords, str):
                        try:
                            import json
                            keywords = json.loads(keywords)
                        except:
                            keywords = [keywords]
                    
                    if isinstance(keywords, list):
                        for keyword in keywords:
                            if keyword.lower() in poi_name or keyword.lower() in poi_type:
                                confidence = getattr(memory, 'confidence_score', 0.5)
                                match_score += confidence * 1.0
        
        return min(match_score, 3.0)  # 限制最高分数
    
    def _extract_common_keywords(self, text1: str, text2: str) -> List[str]:
        """提取两个文本的共同关键词"""
        # 简单的关键词提取逻辑
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        # 过滤掉常见停用词
        stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '很', '非常', '一个', '这个', '那个'}
        words1 = words1 - stop_words
        words2 = words2 - stop_words
        
        # 只保留长度>=2的词
        words1 = {w for w in words1 if len(w) >= 2}
        words2 = {w for w in words2 if len(w) >= 2}
        
        return list(words1 & words2)
    
    async def _save_user_memory(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """保存本次规划的用户记忆到数据库"""

        state.current_step = "save_memory"
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="正在保存用户记忆到数据库"
            ).model_dump()
        )
        
        try:
            # 从Redis获取完整的任务执行记录
            redis_client = await self._get_redis_client()
            task_steps = await redis_client.get_task_steps(state.trace_id)
            business_logs = await redis_client.get_l1_memory(state.trace_id, "business_steps_log")
            
            async with get_db() as db:
                # 构建记忆内容
                destination = state.extracted_entities.get('destination', '未知')
                days = state.extracted_entities.get('days', 1)
                preferences = state.extracted_entities.get('preferences', [])
                
                # 从最终行程中提取关键信息
                memory_content = f"用户在{destination}进行了{days}天的旅行规划"
                keywords = [destination]
                
                if preferences:
                    memory_content += f"，偏好包括：{', '.join(preferences)}"
                    keywords.extend(preferences)
                
                # 从评分后的POI中提取热门类型
                if hasattr(state, 'scored_pois') and state.scored_pois:
                    top_categories = {}
                    for poi in state.scored_pois[:10]:  # 只看前10个高分POI
                        category = poi.get('category', '其他')
                        top_categories[category] = top_categories.get(category, 0) + 1
                    
                    if top_categories:
                        top_category = max(top_categories.items(), key=lambda x: x[1])[0]
                        memory_content += f"，主要关注{top_category}类型的POI"
                        keywords.append(top_category)
                
                # 创建记忆记录
                import json
                from datetime import datetime
                
                memory_data = {
                    'user_id': state.user_id,
                    'memory_content': memory_content,
                    'source_session_id': state.trace_id,
                    # 'memory_type': 'travel_planning',
                    # 'keywords': json.dumps(keywords, ensure_ascii=False),
                    'confidence': 0.8,  # 系统生成的记忆置信度较高
                    # 'source': 'ai_planning',
                    'created_at': datetime.now(),
                    'last_accessed': datetime.now()
                }
                
                # 保存记忆
                await user_memory_crud.create_memory(db, memory_data=memory_data)
                
                # 更新用户画像总结（如果不存在则创建）
                user_summary = await user_summary_crud.get_by_user(db, user_id=state.user_id)
                
                # 构建或更新兴趣标签
                current_interests = []
                if user_summary and hasattr(user_summary, 'keywords'):
                    if isinstance(user_summary.keywords, list):
                        current_interests = user_summary.keywords
                    elif isinstance(user_summary.keywords, str):
                        try:
                            current_interests = json.loads(user_summary.keywords)
                        except:
                            current_interests = [user_summary.keywords]
                
                # 添加新的兴趣标签
                for keyword in keywords:
                    if keyword not in current_interests:
                        current_interests.append(keyword)
                
                # 限制标签数量
                current_interests = [x for x in current_interests if x and isinstance(x, str) and x.strip()]
                current_interests = current_interests[:15]

                summary_data = {
                    'user_id': state.user_id,
                    # 'summary': json.dumps(current_interests, ensure_ascii=False),
                    'keywords': current_interests,
                    # 'travel_style': state.user_profile.travel_style if hasattr(state, 'user_profile') else '休闲',
                    # 'budget_preference': state.user_profile.budget_preference if hasattr(state, 'user_profile') else '中等',
                    'updated_at': datetime.now()
                }
                
                if user_summary:
                    # 更新现有画像
                    await user_summary_crud.update_summary(db, user_id=state.user_id, summary_data=summary_data)
                else:
                    # 创建新画像
                    await user_summary_crud.create_summary(db, summary_data=summary_data)
                
                #更新 ai_planning_sessions 表中的对应任务状态，完成任务状态的闭环
                # 保存AI规划会话记录
                session_data = {
                    # 'id': state.trace_id,
                    'user_id': state.user_id,
                    # 'session_id': state.trace_id,
                    'user_input': json.dumps(state.extracted_entities, ensure_ascii=False),
                    'status': 'SUCCESS',
                    'raw_llm_output': json.dumps(state.orchestrated_itinerary, ensure_ascii=False),
                    # 'final_itinerary_id': state.final_itinerary.trace_id,
                    # 'destination': destination,
                    # 'days': days,
                    'completed_at': datetime.now()
                }
                await ai_planning_session_crud.update_session(db, id=state.trace_id, session_data=session_data)
                
                # 构建MongoDB交互日志数据，包含Redis中的完整执行记录
                interaction_data = {
                    "status": "SUCCESS",
                    "final_output": json.dumps(state.orchestrated_itinerary, ensure_ascii=False),
                    "task_steps": [step.model_dump() for step in task_steps] if task_steps else [],
                    "business_logs": business_logs if business_logs else [],
                    "execution_summary": {
                        "total_steps": len(task_steps) if task_steps else 0,
                        "completed_steps": len([s for s in task_steps if s.status == 'completed']) if task_steps else 0,
                        "failed_steps": len([s for s in task_steps if s.status == 'failed']) if task_steps else 0,
                        "execution_time": (datetime.now() - task_steps[0].timestamp).total_seconds() if task_steps else 0
                    }
                }
                
                # 执行一次 updateOne 操作，用从Redis获取到的完整数据填充在第一阶段创建的"骨架"文档
                mongo_client = await get_mongo_client()
                await mongo_client.update_interaction_log(interaction_id=state.trace_id, interaction_data=interaction_data)
                
                # 清理Redis中的任务数据（可选，根据业务需求决定是否立即清理）
                # await redis_client.cleanup_task_data(state.trace_id)
                
                self.logger.info(f"[SUCCESS] 用户记忆保存完成: {state.user_id}")
                
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.OTHER,
                        content=f"已保存记忆和更新用户画像：{len(keywords)}个关键词，{len(current_interests)}个兴趣标签"
                    ).model_dump()
                )
                
        except Exception as e:
            self.logger.error(f"[ERROR] 保存用户记忆失败: {str(e)}", exc_info=True)
            
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content=f"保存用户记忆时出现错误: {str(e)}"
                ).model_dump()
            )
        
    async def _orchestrate_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """智能行程编排"""
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在进行智能行程编排和时间优化"}
        )
        
        days = state.extracted_entities.get("days", 1)
        if days is None or not isinstance(days, int):
            days = 1
            
        # 基于评分后的POI生成每日行程
        scored_pois = getattr(state, 'scored_pois', [])
        
        # 按类别分离POI
        attractions = [poi for poi in scored_pois if poi.get('category') == '景点']
        foods = [poi for poi in scored_pois if poi.get('category') == '美食']
        
        daily_plans = []
        used_attraction_indices = set()  # 记录已使用的景点索引
        used_food_indices = set()        # 记录已使用的美食索引
        
        for day in range(1, days + 1):
            day_pois = []
            
            # 为每天分配3个景点（不重复）
            attraction_count = 0
            for i, attraction in enumerate(attractions):
                if i not in used_attraction_indices and attraction_count < 3:
                    day_pois.append(attraction)
                    used_attraction_indices.add(i)
                    attraction_count += 1
                if attraction_count >= 3:
                    break
            
            # 为每天分配2个美食（不重复）
            food_count = 0
            for i, food in enumerate(foods):
                if i not in used_food_indices and food_count < 2:
                    day_pois.append(food)
                    used_food_indices.add(i)
                    food_count += 1
                if food_count >= 2:
                    break
            
            # 转换为POIInfo对象
            poi_infos = []
            for poi in day_pois:
                # 从POI的经纬度字符串中解析
                lon, lat = (0, 0)
                # location_str = poi.get('location', '')
                # if ',' in location_str:
                #     try:
                #         lon, lat = map(float, location_str.split(','))
                #     except ValueError:
                #         pass # 保持默认值
                location_val = poi.get('location', None)
                # 优先从location_val.address获取
                location_address = getattr(location_val, 'address', '')
                if isinstance(location_address, list):
                    location_address = ''
                if not location_address:
                    raw_address = poi.get('address', '')
                    if isinstance(raw_address, list):
                        raw_address = ''
                    location_address = raw_address
                location = Location(
                    longitude=getattr(location_val, 'longitude', 0),
                    latitude=getattr(location_val, 'latitude', 0),
                    name=getattr(location_val, 'name', ''),
                    address=location_address
                )
                
                # 获取第一个照片URL作为image_url
                image_url = None
                photos = poi.get('photos', [])
                if photos and len(photos) > 0:
                    first_photo = photos[0]
                    if isinstance(first_photo, dict) and 'url' in first_photo:
                        image_url = first_photo['url']
                
                poi_info = POIInfo(
                    poi_instance_id=str(uuid.uuid4()),  # <--- FIX: 生成唯一的实例ID
                    poi_id=poi.get('id', ''),
                    name=poi.get('name', ''),
                    category=poi.get('category', '其他'),
                    image_url=image_url,
                    rating=float(poi.get('rating', 0.0)) if poi.get('rating') else 0.0,
                    address=location_address,
                    location=location,
                    estimated_duration_min=120,  # 默认2小时
                    description=poi.get('type', ''),
                    tips="建议提前预约",
                    tel=poi.get('tel', ''),
                    price=poi.get('price', 0),
                )
                poi_infos.append(poi_info)
                
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=poi_infos
            )
            daily_plans.append(daily_plan)
        
        print("daily_plans end")    
        state.tool_results["daily_plans"] = daily_plans

    async def _analyze_driving_context(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析驾驶情境（A.2）"""
        try:
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content="正在分析您的驾驶情境和车辆信息"
                ).model_dump()
            )

            # 这里可以添加具体的驾驶情境分析逻辑
            # 目前使用简化版本
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content="已分析驾驶情境，将为您规划最适合的自驾路线"
                ).model_dump()
            )

        except Exception as e:
            self.logger.error(f"[ERROR] 分析驾驶情境失败: {e}", exc_info=True)
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.OTHER,
                    content="驾驶情境分析遇到问题，将使用默认设置"
                ).model_dump()
            )

    async def _analyze_attraction_preferences(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析景点偏好（A.3）"""
        try:
            print(f"🎯 [景点偏好分析] 开始分析用户{state.user_id}的景点偏好")
            self.logger.info(f"开始分析用户{state.user_id}的景点偏好")

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                    content="正在基于您的历史数据分析景点偏好类型..."
                ).model_dump()
            )

            # 基于真实数据库数据分析景点偏好
            preferences = []
            analysis_details = []

            if hasattr(state, 'user_profile') and state.user_profile:
                user_tags = self._get_user_tags(state.user_profile)
                print(f"📊 [景点偏好分析] 用户画像标签: {user_tags}")
                self.logger.info(f"用户画像标签: {user_tags}")

                # 从用户画像标签中分析偏好
                for tag in user_tags[:5]:  # 分析前5个标签
                    if any(keyword in tag for keyword in ['历史', '文化', '古建筑', '摄影']):
                        preferences.append('历史文化景点')
                        analysis_details.append(f"从您的'{tag}'标签看出您对历史文化感兴趣")
                    elif any(keyword in tag for keyword in ['自然', '风光', '风景', '山水']):
                        preferences.append('自然风光景点')
                        analysis_details.append(f"从您的'{tag}'标签看出您喜欢自然风光")
                    elif any(keyword in tag for keyword in ['现代', '科技', '都市', '城市']):
                        preferences.append('现代都市景点')
                        analysis_details.append(f"从您的'{tag}'标签看出您对现代都市感兴趣")
                    elif any(keyword in tag for keyword in ['驾驶', '自驾', '停车']):
                        analysis_details.append(f"从您的'{tag}'标签看出您偏好自驾出行")

                # 从旅行风格分析
                travel_style = getattr(state.user_profile, 'travel_style', '休闲')
                print(f"📊 [景点偏好分析] 旅行风格: {travel_style}")
                if travel_style == 'ADVENTURE':
                    preferences.append('探险类景点')
                    analysis_details.append("您的冒险型旅行风格显示您喜欢挑战性景点")
                elif travel_style == 'FAMILY':
                    preferences.append('亲子友好景点')
                    analysis_details.append("您的家庭型旅行风格显示您需要适合全家的景点")

            # 从用户记忆中分析历史偏好
            if hasattr(state, 'user_memories') and state.user_memories:
                print(f"📊 [景点偏好分析] 分析{len(state.user_memories)}条历史记忆")
                memory_analysis = []
                for memory in state.user_memories[:10]:  # 分析最近10条记忆
                    memory_content = memory.get('memory_content', '') if isinstance(memory, dict) else getattr(memory, 'memory_content', '')
                    if '景点' in memory_content:
                        memory_analysis.append(memory_content)

                if memory_analysis:
                    analysis_details.append(f"从您的{len(memory_analysis)}条历史记忆中发现您经常关注景点类型的POI")

            # 如果没有明确偏好，使用默认分析
            if not preferences:
                preferences = ['综合性景点', '当地特色景点']
                analysis_details.append("基于通用偏好为您推荐")

            # 去重偏好
            preferences = list(set(preferences))

            print(f"✅ [景点偏好分析] 分析结果: {preferences}")
            print(f"📝 [景点偏好分析] 分析详情: {analysis_details}")

            # 构建分析结果
            content = f"根据您的喜好，我发现您对{', '.join(preferences)}特别感兴趣。我会重点为您留意这类地方，并确保它们停车方便。"
            if analysis_details:
                content += f" 分析依据：{'; '.join(analysis_details[:2])}。"

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                    content=content
                ).model_dump()
            )

        except Exception as e:
            print(f"❌ [景点偏好分析] 分析失败: {e}")
            self.logger.error(f"[ERROR] 分析景点偏好失败: {e}", exc_info=True)
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ATTRACTION_RECOMMENDATION,
                    content="景点偏好分析遇到问题，将推荐热门景点"
                ).model_dump()
            )

    async def _analyze_food_preferences(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析美食偏好（A.4）"""
        try:
            print(f"🍽️ [美食偏好分析] 开始分析用户{state.user_id}的美食偏好")
            self.logger.info(f"开始分析用户{state.user_id}的美食偏好")

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.FOOD_RECOMMENDATION,
                    content="正在基于您的历史数据分析美食偏好..."
                ).model_dump()
            )

            # 基于真实数据库数据分析美食偏好
            food_prefs = []
            analysis_details = []

            if hasattr(state, 'user_profile') and state.user_profile:
                # 从预算偏好分析
                budget_pref = getattr(state.user_profile, 'budget_preference', '中等')
                print(f"📊 [美食偏好分析] 预算偏好: {budget_pref}")
                if budget_pref == '经济型':
                    food_prefs.append('当地小吃')
                    analysis_details.append("根据您的经济型预算偏好，推荐当地小吃")
                elif budget_pref == '豪华型':
                    food_prefs.append('精品餐厅')
                    analysis_details.append("根据您的豪华型预算偏好，推荐精品餐厅")
                else:
                    food_prefs.append('特色餐厅')
                    analysis_details.append("根据您的中等预算偏好，推荐特色餐厅")

                # 从用户标签分析美食偏好
                user_tags = self._get_user_tags(state.user_profile)
                for tag in user_tags[:5]:
                    if any(keyword in tag for keyword in ['美食', '吃', '餐厅', '小吃']):
                        food_prefs.append('美食探索')
                        analysis_details.append(f"从您的'{tag}'标签看出您对美食有特别兴趣")
                    elif any(keyword in tag for keyword in ['文化', '历史']):
                        food_prefs.append('传统美食')
                        analysis_details.append(f"从您的'{tag}'标签看出您可能喜欢传统美食")

            # 从用户记忆中分析美食偏好
            if hasattr(state, 'user_memories') and state.user_memories:
                print(f"📊 [美食偏好分析] 分析{len(state.user_memories)}条历史记忆")
                food_memory_count = 0
                for memory in state.user_memories[:10]:
                    memory_content = memory.get('memory_content', '') if isinstance(memory, dict) else getattr(memory, 'memory_content', '')
                    if any(keyword in memory_content for keyword in ['美食', '餐厅', '吃']):
                        food_memory_count += 1

                if food_memory_count > 0:
                    analysis_details.append(f"从您的{food_memory_count}条历史记忆中发现您经常关注美食类型的POI")

            if not food_prefs:
                food_prefs = ['当地特色美食']
                analysis_details.append("基于通用偏好为您推荐")

            # 去重偏好
            food_prefs = list(set(food_prefs))

            print(f"✅ [美食偏好分析] 分析结果: {food_prefs}")
            print(f"📝 [美食偏好分析] 分析详情: {analysis_details}")

            content = f"好的，在吃的方面，我会特别为您留意{', '.join(food_prefs)}，并帮您看好附近的停车位。"
            if analysis_details:
                content += f" 分析依据：{'; '.join(analysis_details[:2])}。"

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.FOOD_RECOMMENDATION,
                    content=content
                ).model_dump()
            )

        except Exception as e:
            print(f"❌ [美食偏好分析] 分析失败: {e}")
            self.logger.error(f"[ERROR] 分析美食偏好失败: {e}", exc_info=True)
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.FOOD_RECOMMENDATION,
                    content="美食偏好分析遇到问题，将推荐热门餐厅"
                ).model_dump()
            )

    async def _analyze_accommodation_preferences(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """分析住宿偏好（A.5）"""
        try:
            print(f"🏨 [住宿偏好分析] 开始分析用户{state.user_id}的住宿偏好")
            self.logger.info(f"开始分析用户{state.user_id}的住宿偏好")

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ACCOMMODATION_RECOMMENDATION,
                    content="正在基于您的历史数据分析住宿偏好..."
                ).model_dump()
            )

            # 基于真实数据库数据分析住宿偏好
            accommodation_prefs = []
            budget_range = "中等价位"
            analysis_details = []

            if hasattr(state, 'user_profile') and state.user_profile:
                # 从预算偏好分析
                budget_pref = getattr(state.user_profile, 'budget_preference', '中等')
                print(f"📊 [住宿偏好分析] 预算偏好: {budget_pref}")
                if budget_pref == '经济型':
                    accommodation_prefs.append('经济型连锁酒店')
                    budget_range = "200-400元"
                    analysis_details.append("根据您的经济型预算偏好，推荐经济型酒店")
                elif budget_pref == '豪华型':
                    accommodation_prefs.append('五星级酒店')
                    budget_range = "800-1500元"
                    analysis_details.append("根据您的豪华型预算偏好，推荐五星级酒店")
                else:
                    accommodation_prefs.append('舒适型酒店')
                    budget_range = "400-800元"
                    analysis_details.append("根据您的中等预算偏好，推荐舒适型酒店")

                # 从用户标签分析住宿偏好
                user_tags = self._get_user_tags(state.user_profile)
                for tag in user_tags[:5]:
                    if any(keyword in tag for keyword in ['舒适', '品质']):
                        accommodation_prefs.append('品质酒店')
                        analysis_details.append(f"从您的'{tag}'标签看出您注重住宿品质")
                    elif any(keyword in tag for keyword in ['性价比', '经济']):
                        if '经济型连锁酒店' not in accommodation_prefs:
                            accommodation_prefs.append('性价比酒店')
                        analysis_details.append(f"从您的'{tag}'标签看出您注重性价比")

                # 从旅行风格分析
                travel_style = getattr(state.user_profile, 'travel_style', '休闲')
                print(f"📊 [住宿偏好分析] 旅行风格: {travel_style}")
                if travel_style == 'FAMILY':
                    accommodation_prefs.append('家庭友好酒店')
                    analysis_details.append("您的家庭型旅行风格显示您需要适合全家的住宿")

            # 从用户记忆中分析住宿偏好
            if hasattr(state, 'user_memories') and state.user_memories:
                print(f"📊 [住宿偏好分析] 分析{len(state.user_memories)}条历史记忆")
                hotel_memory_count = 0
                for memory in state.user_memories[:10]:
                    memory_content = memory.get('memory_content', '') if isinstance(memory, dict) else getattr(memory, 'memory_content', '')
                    if any(keyword in memory_content for keyword in ['酒店', '住宿', '宾馆']):
                        hotel_memory_count += 1

                if hotel_memory_count > 0:
                    analysis_details.append(f"从您的{hotel_memory_count}条历史记忆中发现您经常关注住宿类型的POI")

            if not accommodation_prefs:
                accommodation_prefs = ['舒适型酒店']
                analysis_details.append("基于通用偏好为您推荐")

            # 去重偏好
            accommodation_prefs = list(set(accommodation_prefs))

            print(f"✅ [住宿偏好分析] 分析结果: {accommodation_prefs}, 预算范围: {budget_range}")
            print(f"📝 [住宿偏好分析] 分析详情: {analysis_details}")

            content = f"住宿方面，我将为您寻找符合{budget_range}、{', '.join(accommodation_prefs)}，并且停车无忧的酒店。"
            if analysis_details:
                content += f" 分析依据：{'; '.join(analysis_details[:2])}。"

            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ACCOMMODATION_RECOMMENDATION,
                    content=content
                ).model_dump()
            )

        except Exception as e:
            self.logger.error(f"[ERROR] 分析住宿偏好失败: {e}", exc_info=True)
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.THINKING_STEP,
                payload=ThinkingStepPayload(
                    category=ThinkingCategory.ACCOMMODATION_RECOMMENDATION,
                    content="住宿偏好分析遇到问题，将推荐热门酒店"
                ).model_dump()
            )


