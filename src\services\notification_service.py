# src/services/notification_service.py
import json
import logging
from typing import Dict, Any
from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)


class NotificationService:
    """
    事件通知服务
    
    基于Redis Pub/Sub的事件驱动通知系统，负责将业务事件发布到消息总线。
    实现业务逻辑与推送机制的完全解耦。
    """

    def __init__(self, redis_client: RedisClient):
        """
        初始化通知服务
        
        Args:
            redis_client: Redis客户端实例
        """
        self.redis = redis_client.client
        logger.info("NotificationService initialized")

    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        """
        发布事件到Redis频道
        
        Args:
            task_id: 任务ID，用于构建频道名称
            event_data: 事件数据
        """
        channel = f"task:{task_id}"
        message = json.dumps(event_data, ensure_ascii=False)
        
        try:
            await self.redis.publish(channel, message)
            logger.debug(f"Published to {channel}: {message}")
        except Exception as e:
            logger.error(f"Failed to publish event to {channel}: {str(e)}")
            raise

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        """
        发送步骤开始事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称
            title: 步骤标题
            message: 步骤消息
        """
        event = {
            "event": "step_start",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "title": title,
                "message": message,
                "timestamp": self._get_timestamp()
            }
        }
        await self._publish(task_id, event)

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        """
        发送步骤结束事件
        
        Args:
            task_id: 任务ID
            step_name: 步骤名称
            status: 步骤状态 (success/error)
            result: 步骤结果数据
        """
        event = {
            "event": "step_end",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "status": status,
                "result": result or {},
                "timestamp": self._get_timestamp()
            }
        }
        await self._publish(task_id, event)

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """
        发送最终结果事件
        
        Args:
            task_id: 任务ID
            final_data: 最终结果数据
        """
        event = {
            "event": "complete",
            "data": final_data
        }
        await self._publish(task_id, event)
        
        # 发送流结束信号
        await self._publish(task_id, {"event": "eos"})

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        """
        发送错误事件
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            step_name: 出错的步骤名称
        """
        event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message,
                "timestamp": self._get_timestamp()
            }
        }
        await self._publish(task_id, event)
        
        # 发送流结束信号
        await self._publish(task_id, {"event": "eos"})

    async def notify_progress(self, task_id: str, stage: str, progress: int, message: str):
        """
        发送进度更新事件
        
        Args:
            task_id: 任务ID
            stage: 当前阶段
            progress: 进度百分比 (0-100)
            message: 进度消息
        """
        event = {
            "event": "progress",
            "data": {
                "stage": stage,
                "progress": progress,
                "message": message,
                "timestamp": self._get_timestamp()
            }
        }
        await self._publish(task_id, event)

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
