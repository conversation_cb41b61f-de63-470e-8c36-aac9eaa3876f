-- 切换到用户档案中心数据库
USE `dh_user_profile`;

-- 在 `users` 表中增加积分余额和形象槽位上限字段
ALTER TABLE `users`
ADD COLUMN `points_balance` INT NOT NULL DEFAULT 0 COMMENT '用户当前积分余额' AFTER `status`,
ADD COLUMN `avatar_slot_limit` INT NOT NULL DEFAULT 3 COMMENT '用户拥有的数字人形象槽位上限' AFTER `points_balance`;


-- 创建一个新的表，用于记录所有积分的收支明细
CREATE TABLE IF NOT EXISTS `point_transactions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '交易记录主键, 自增',
  `user_id` INT NOT NULL COMMENT '关联到 users.id',
  `amount` INT NOT NULL COMMENT '积分变动数量 (正数为增加, 负数为消耗)',
  `balance_after` INT NOT NULL COMMENT '本次交易后用户的积分余额',
  `transaction_type` VARCHAR(50) NOT NULL COMMENT '交易类型 (例如: SKILL_UNLOCK, AVATAR_GENERATION, AVATAR_SLOT_UNLOCK, DAILY_LOGIN_REWARD)',
  `description` VARCHAR(255) NOT NULL COMMENT '交易描述 (例如: "解锁技能: 亲子学堂", "首次登录奖励")',
  `related_entity_id` VARCHAR(50) NULL COMMENT '关联的实体ID (例如: 技能ID, 形象实例ID)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '交易发生时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id_created_at` (`user_id`, `created_at` DESC) COMMENT '快速查询用户积分历史'
) ENGINE=InnoDB COMMENT='用户积分收支明细表';


-- 如果 Schema 不存在，则创建
CREATE SCHEMA IF NOT EXISTS `dh_ailab` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到目标 Schema
USE `dh_ailab`;

-- =================================================================
-- 表 1: `ai_skills` - AI技能字典表
-- 描述: 定义了系统中所有可供用户解锁的AI技能。对应原型中的“亲子学堂”、“随车导游”等。
-- =================================================================
CREATE TABLE IF NOT EXISTS `ai_skills` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '技能主键, 自增',
  `skill_key` VARCHAR(50) NOT NULL COMMENT '技能的唯一英文标识 (例如: parent_child_school, trip_guide)',
  `name` VARCHAR(50) NOT NULL COMMENT '技能名称 (例如: 亲子学堂)',
  `description` TEXT NOT NULL COMMENT '技能的详细描述',
  `category` VARCHAR(50) NULL COMMENT '技能分类 (例如: 家庭, 出行, 娱乐)',
  `icon_url` VARCHAR(255) NULL COMMENT '技能图标的URL',
  `unlock_cost_points` INT NOT NULL DEFAULT 0 COMMENT '解锁该技能所需消耗的积分 (0表示免费或默认拥有)',
  `is_public` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否对所有用户可见',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_skill_key_unique` (`skill_key`)
) ENGINE=InnoDB COMMENT='AI技能字典表，定义了所有可用的AI能力';


-- =================================================================
-- 表 2: `user_unlocked_skills` - 用户已解锁技能关联表
-- 描述: 记录用户与他们已解锁技能的关系。
-- =================================================================
CREATE TABLE IF NOT EXISTS `user_unlocked_skills` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键, 自增',
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `skill_id` INT NOT NULL COMMENT '逻辑外键, 关联到 ai_skills.id',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '用户是否开启此技能的主动服务 (对应原型中的“主动服务开关”)',
  `unlocked_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '技能解锁时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_skill_unique` (`user_id`, `skill_id`) COMMENT '确保一个用户对一个技能只有一条解锁记录'
) ENGINE=InnoDB COMMENT='用户已解锁的技能关联表';


-- =================================================================
-- 表 3: `vpa_personas` - VPA预设人设字典表
-- 描述: 存储系统中提供的基础VPA形象，如“默认”、“后浪”、“小理”等。
-- =================================================================
CREATE TABLE IF NOT EXISTS `vpa_personas` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '人设主键, 自增',
  `name` VARCHAR(50) NOT NULL COMMENT '人设名称 (例如: 默认, 后浪)',
  `description` VARCHAR(255) NULL COMMENT '人设简短描述',
  `thumbnail_url` VARCHAR(255) NOT NULL COMMENT '人设的静态缩略图URL',
  `asset_id` VARCHAR(100) NOT NULL COMMENT '关联到远端AI资产库的数字人资产ID',
  `default_action_id` VARCHAR(100) NULL COMMENT '该人设的默认待机动作ID',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为新用户的默认人设',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='VPA预设人设（形象）字典表';


-- =================================================================
-- 表 4: `user_vpa_instances` - 用户VPA形象实例表
-- 描述: 核心表！存储每个用户拥有或制作的VPA形象实例，包括自定义的。对应“我的百变形象”。
-- =================================================================
CREATE TABLE IF NOT EXISTS `user_vpa_instances` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'VPA实例主键, 自增',
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `nickname` VARCHAR(50) NOT NULL COMMENT '用户为该形象实例设置的昵称 (例如: 我的专属助理小狐狸)',
  `status` ENUM('ACTIVE', 'GENERATING', 'FAILED', 'ARCHIVED') NOT NULL DEFAULT 'ACTIVE' COMMENT '实例状态 (ACTIVE:可用, GENERATING:制作中, FAILED:制作失败, ARCHIVED:已归档)',
  `is_currently_active` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为用户当前正在使用的形象 (一个用户同时只能有一个激活)',
  
  -- 形象资产信息
  `asset_id` VARCHAR(100) NOT NULL COMMENT '关联到远端AI资产库的数字人资产ID',
  `action_id` VARCHAR(100) NULL COMMENT '当前使用的动作ID (可切换)',
  `thumbnail_url` VARCHAR(255) NOT NULL COMMENT '该实例的静态缩略图URL',
  
  -- 自定义配置
  `custom_wake_word` VARCHAR(20) NULL COMMENT '自定义唤醒词',
  `custom_response` VARCHAR(50) NULL COMMENT '自定义应答语',
  
  -- 生成来源信息 (用于追溯和重新制作)
  `generation_source` ENUM('PRESET', 'UGC') NOT NULL COMMENT '来源 (PRESET:系统预设, UGC:用户生成)',
  `base_persona_id` INT NULL COMMENT '如果来源是PRESET, 关联到 vpa_personas.id',
  `generation_prompt` TEXT NULL COMMENT '如果来源是UGC, 记录当时用户输入的Prompt',
  `generation_style` VARCHAR(50) NULL COMMENT '如果来源是UGC, 记录当时选择的图像风格',
  
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '实例创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '实例最后更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB COMMENT='用户拥有的VPA形象实例表';