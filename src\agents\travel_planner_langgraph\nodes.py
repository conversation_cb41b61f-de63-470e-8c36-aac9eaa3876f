"""
TravelPlannerAgent LangGraph节点函数

实现了旅行规划Agent的所有节点函数，支持双模运行和状态管理。
集成NotificationService以支持实时推送。
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .state import TravelPlanState, ProcessingStage, PlanningMode, add_event_to_state, update_state_stage
from src.services.notification_service import NotificationService
from src.database.redis_client import get_redis_client
from ..services.analysis_service import AnalysisService
from ..services.user_profile_service import UserProfileService
from ..services.amap_service import AmapService
from ..services.reasoning_service import ReasoningService
from ..services.memory_service import MemoryService
from src.services.user_profile_database_service import get_user_profile_database_service
from src.prompts.loader import apply_prompt_template
from src.prompts import apply_prompt_template

logger = logging.getLogger(__name__)


async def core_intent_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    核心意图分析节点 - 只负责核心意图分析

    分析用户的原始查询，提取核心旅行意图和关键信息。
    """
    logger.info(f"开始核心意图分析 - Session: {state['session_id']}")

    # 获取NotificationService实例（从state中传递）
    notification_service = state.get("notification_service")
    task_id = state.get("session_id")

    try:
        # 发送步骤开始事件
        if notification_service and task_id:
            await notification_service.notify_step_start(
                task_id=task_id,
                step_name="core_intent_analysis",
                title="解析用户需求和画像",
                message="正在分析您的旅行意图..."
            )

        # 添加开始事件（保持向后兼容）
        state = add_event_to_state(state, "stage_start", {
            "stage": "intent_analysis",
            "message": "开始分析您的旅行意图..."
        })

        # 获取用户的真实数据库画像
        user_profile_db_service = get_user_profile_database_service()
        comprehensive_profile = await user_profile_db_service.get_user_comprehensive_profile(state["user_id"])
        state["comprehensive_user_profile"] = comprehensive_profile

        # 格式化用户画像为分析文本
        user_profile_text = user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile)

        logger.info(f"用户 {state['user_id']} 数据库画像完整度: {comprehensive_profile.get('profile_completeness', 0):.2f}")
        logger.info(f"用户画像摘要: {user_profile_text[:200]}...")

        # 构建提示词变量（使用真实数据库数据）
        template_vars = {
            "original_query": state["original_query"],
            "user_profile_text": user_profile_text,
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "analysis_context": comprehensive_profile.get("analysis_context", {})
        }

        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01_core_intent_analyzer",
            template_vars
        )

        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        core_intent = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="core_intent_schema"
        )
        state["core_intent"] = core_intent

        # 将数据库画像数据设置到前端期望的字段中
        state["user_profile"] = comprehensive_profile.get("user_profile")
        state["user_memories"] = comprehensive_profile.get("user_memories", [])
        state["travel_preferences"] = comprehensive_profile.get("travel_preferences")

        # 记录工具调用
        state["tool_calls"].append({
            "node": "core_intent_analyzer",
            "timestamp": datetime.now().isoformat(),
            "input": template_vars,
            "output": core_intent
        })

        # 发送步骤结束事件
        if notification_service and task_id:
            await notification_service.notify_step_end(
                task_id=task_id,
                step_name="core_intent_analysis",
                status="success",
                result={
                    "destinations": core_intent.get("destinations", []),
                    "days": core_intent.get("days"),
                    "travel_theme": core_intent.get("travel_theme"),
                    "confidence_score": core_intent.get("confidence_score", 0)
                }
            )

        # 添加完成事件（保持向后兼容）
        state = add_event_to_state(state, "intent_analyzed", {
            "destinations": core_intent.get("destinations", []),
            "days": core_intent.get("days"),
            "travel_theme": core_intent.get("travel_theme"),
            "confidence_score": core_intent.get("confidence_score", 0)
        })

        logger.info(f"核心意图分析完成 - 目的地: {core_intent.get('destinations')}")

    except Exception as e:
        logger.error(f"核心意图分析失败: {str(e)}")

        # 发送错误事件
        if notification_service and task_id:
            await notification_service.notify_error(
                task_id=task_id,
                error_message=f"意图分析失败: {str(e)}",
                step_name="core_intent_analysis"
            )

        state["has_error"] = True
        state["error_message"] = f"意图分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "intent_analysis",
            "error": str(e)
        })

    return state


async def poi_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    景点偏好分析节点

    分析用户的景点偏好类型和深度要求。
    """
    logger.info(f"开始景点偏好分析 - Session: {state['session_id']}")
    logger.info(f"景点偏好分析 - 函数被调用")

    # 初始化NotificationService
    notification_service = NotificationService(session_id=state.get("session_id"))
    logger.info(f"景点偏好分析 - NotificationService初始化完成")

    try:
        # 初始化SSE事件列表
        if "sse_events" not in state:
            state["sse_events"] = []

        # 发送景点偏好分析开始事件
        step_id, sse_event_start = await notification_service.notify_step_start(
            step_name="poi_preference_analysis",
            title="景点偏好类型",
            message="正在分析您的景点偏好..."
        )
        state["sse_events"].append(sse_event_start)

        # 获取核心意图和用户画像
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})
        user_memories = comprehensive_profile.get("user_memories", [])
        travel_preferences = comprehensive_profile.get("travel_preferences", {})

        # 构建提示词变量
        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": user_memories,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }

        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/02_attraction_preference_analyzer",
            template_vars
        )

        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        poi_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="attraction_preference_schema"
        )
        state["poi_preferences"] = poi_preferences

        # 记录工具调用
        state["tool_calls"].append({
            "node": "poi_preference_analyzer",
            "timestamp": datetime.now().isoformat(),
            "input": {"travel_preferences": travel_preferences},
            "output": poi_preferences
        })

        # 发送景点偏好分析结束事件
        sse_event_end = await notification_service.notify_step_end(
            step_id=step_id,
            status="success",
            result=poi_preferences,
            message="景点偏好分析完成"
        )
        state["sse_events"].append(sse_event_end)

        logger.info(f"景点偏好分析完成 - 偏好类型: {poi_preferences.get('preferred_types')}")

    except Exception as e:
        logger.error(f"景点偏好分析失败: {str(e)}")

        # 发送错误事件
        error_sse_event = await notification_service.notify_error(
            error_message=f"景点偏好分析失败: {str(e)}",
            step_id=step_id if 'step_id' in locals() else None
        )
        state["sse_events"].append(error_sse_event)

        state["has_error"] = True
        state["error_message"] = f"景点偏好分析失败: {str(e)}"

    return state


async def food_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    美食偏好分析节点

    分析用户的美食偏好和餐厅类型要求。
    """
    logger.info(f"开始美食偏好分析 - Session: {state['session_id']}")

    # 初始化NotificationService
    notification_service = NotificationService(session_id=state.get("session_id"))

    try:
        # 初始化SSE事件列表
        if "sse_events" not in state:
            state["sse_events"] = []

        # 发送美食偏好分析开始事件
        step_id, sse_event_start = await notification_service.notify_step_start(
            step_name="food_preference_analysis",
            title="美食偏好",
            message="正在分析您的美食偏好..."
        )
        state["sse_events"].append(sse_event_start)

        # 获取核心意图和用户画像
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})
        user_memories = comprehensive_profile.get("user_memories", [])
        travel_preferences = comprehensive_profile.get("travel_preferences", {})

        # 构建提示词变量
        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": user_memories,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days"),
            "travelers": core_intent.get("travelers")
        }

        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/03_food_preference_analyzer",
            template_vars
        )

        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        food_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="food_preference_schema"
        )
        state["food_preferences"] = food_preferences

        # 记录工具调用
        state["tool_calls"].append({
            "node": "food_preference_analyzer",
            "timestamp": datetime.now().isoformat(),
            "input": {"travel_preferences": travel_preferences},
            "output": food_preferences
        })

        # 发送美食偏好分析结束事件
        sse_event_end = await notification_service.notify_step_end(
            step_id=step_id,
            status="success",
            result=food_preferences,
            message="美食偏好分析完成"
        )
        state["sse_events"].append(sse_event_end)

        logger.info(f"美食偏好分析完成 - 口味偏好: {food_preferences.get('taste_preferences')}")

    except Exception as e:
        logger.error(f"美食偏好分析失败: {str(e)}")

        # 发送错误事件
        error_sse_event = await notification_service.notify_error(
            error_message=f"美食偏好分析失败: {str(e)}",
            step_id=step_id if 'step_id' in locals() else None
        )
        state["sse_events"].append(error_sse_event)

        state["has_error"] = True
        state["error_message"] = f"美食偏好分析失败: {str(e)}"

    return state


async def accommodation_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    住宿偏好分析节点

    分析用户的住宿偏好和预算要求。
    """
    logger.info(f"开始住宿偏好分析 - Session: {state['session_id']}")

    # 初始化NotificationService
    notification_service = NotificationService(session_id=state.get("session_id"))

    try:
        # 初始化SSE事件列表
        if "sse_events" not in state:
            state["sse_events"] = []

        # 发送住宿偏好分析开始事件
        step_id, sse_event_start = await notification_service.notify_step_start(
            step_name="accommodation_preference_analysis",
            title="住宿偏好",
            message="正在分析您的住宿偏好..."
        )
        state["sse_events"].append(sse_event_start)

        # 获取核心意图和用户画像
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})
        user_memories = comprehensive_profile.get("user_memories", [])
        travel_preferences = comprehensive_profile.get("travel_preferences", {})

        # 构建提示词变量
        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": user_memories,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days"),
            "travelers": core_intent.get("travelers")
        }

        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/04_accommodation_preference_analyzer",
            template_vars
        )

        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        accommodation_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="accommodation_preference_schema"
        )
        state["accommodation_preferences"] = accommodation_preferences

        # 记录工具调用
        state["tool_calls"].append({
            "node": "accommodation_preference_analyzer",
            "timestamp": datetime.now().isoformat(),
            "input": {"travel_preferences": travel_preferences},
            "output": accommodation_preferences
        })

        # 发送住宿偏好分析结束事件
        sse_event_end = await notification_service.notify_step_end(
            step_id=step_id,
            status="success",
            result=accommodation_preferences,
            message="住宿偏好分析完成"
        )
        state["sse_events"].append(sse_event_end)

        logger.info(f"住宿偏好分析完成 - 预算范围: {accommodation_preferences.get('budget_range')}")

    except Exception as e:
        logger.error(f"住宿偏好分析失败: {str(e)}")

        # 发送错误事件
        error_sse_event = await notification_service.notify_error(
            error_message=f"住宿偏好分析失败: {str(e)}",
            step_id=step_id if 'step_id' in locals() else None
        )
        state["sse_events"].append(error_sse_event)

        state["has_error"] = True
        state["error_message"] = f"住宿偏好分析失败: {str(e)}"

    return state


async def multi_city_strategy_node(state: TravelPlanState) -> TravelPlanState:
    """
    多城市策略分析节点
    
    当涉及多个目的地时，制定最优的路线策略和时间分配。
    """
    logger.info(f"开始多城市策略分析 - Session: {state['session_id']}")
    
    try:
        core_intent = state.get("core_intent", {})
        destinations = core_intent.get("destinations", [])
        
        # 如果只有一个目的地，跳过此节点
        if len(destinations) <= 1:
            logger.info("单一目的地，跳过多城市策略分析")
            return state
        
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "multi_city_strategy",
            "message": f"正在为您的{len(destinations)}个目的地制定最优路线..."
        })
        
        # 构建提示词变量 - 意图理解阶段不查询距离矩阵
        template_vars = {
            "destinations": destinations,
            "total_days": core_intent.get("days"),
            "user_preferences": core_intent.get("preferences"),
            "transportation_mode": core_intent.get("transportation", {}).get("primary_mode")
        }
        
        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01a_multi_city_strategy_analyzer",
            template_vars
        )
        
        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        multi_city_strategy = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="multi_city_strategy_schema"
        )
        state["multi_city_strategy"] = multi_city_strategy
        
        # 添加完成事件
        state = add_event_to_state(state, "strategy_planned", {
            "strategy_type": multi_city_strategy.get("strategy_type"),
            "recommended_order": multi_city_strategy.get("recommended_order"),
            "total_travel_time": multi_city_strategy.get("total_travel_time")
        })
        
        logger.info(f"多城市策略分析完成 - 策略: {multi_city_strategy.get('strategy_type')}")
        
    except Exception as e:
        logger.error(f"多城市策略分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"多城市策略分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "multi_city_strategy",
            "error": str(e)
        })
    
    return state


async def driving_context_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    驾驶情境分析节点
    
    分析用户的车辆信息和驾驶需求，确定规划模式。
    """
    logger.info(f"开始驾驶情境分析 - Session: {state['session_id']}")
    
    try:
        core_intent = state.get("core_intent", {})
        if not core_intent:
            logger.error("核心意图信息缺失，无法进行驾驶情境分析")
            state["has_error"] = True
            state["error_message"] = "核心意图信息缺失"
            return state

        transportation = core_intent.get("transportation", {})
        if not transportation:
            logger.warning("交通信息缺失，设置默认自驾模式")
            transportation = {"primary_mode": "self_driving"}
            core_intent["transportation"] = transportation
            state["core_intent"] = core_intent

        # 确保为自驾出行（这是应用的大前提）
        primary_mode = transportation.get("primary_mode")
        if primary_mode != "self_driving":
            logger.warning(f"检测到非自驾模式: {primary_mode}，强制设置为自驾模式")
            transportation["primary_mode"] = "self_driving"
            core_intent["transportation"] = transportation
            state["core_intent"] = core_intent
        
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "driving_context",
            "message": "正在分析您的驾驶需求和车辆信息..."
        })
        
        # 构建提示词变量
        template_vars = {
            "user_vehicle_info": state.get("vehicle_info"),
            "destinations": core_intent.get("destinations"),
            "total_days": core_intent.get("days"),
            "user_preferences": core_intent.get("preferences")
        }
        
        # 应用提示词模板
        messages = apply_prompt_template(
            "travel_planner/01b_driving_context_analyzer",
            template_vars
        )
        
        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")
        driving_context = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="driving_context_schema"
        )
        state["driving_context"] = driving_context
        
        # 根据分析结果设置规划模式
        driving_strategy = driving_context.get("driving_strategy")
        if driving_strategy == "range_aware":
            state["planning_mode"] = PlanningMode.RANGE_AWARE.value
        else:
            state["planning_mode"] = PlanningMode.GENERAL_ASSISTANCE.value
        
        # 添加完成事件
        state = add_event_to_state(state, "driving_analyzed", {
            "driving_strategy": driving_strategy,
            "planning_mode": state["planning_mode"],
            "vehicle_type": driving_context.get("vehicle_analysis", {}).get("vehicle_type")
        })
        
        logger.info(f"驾驶情境分析完成 - 策略: {driving_strategy}")
        
    except Exception as e:
        logger.error(f"驾驶情境分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"驾驶情境分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "driving_context",
            "error": str(e)
        })
    
    return state


async def preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    偏好分析节点
    
    分析用户的景点、美食等偏好，构建偏好画像。
    """
    logger.info(f"开始偏好分析 - Session: {state['session_id']}")
    
    try:
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "preference_analysis",
            "message": "正在分析您的旅行偏好..."
        })
        
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})

        # 获取用户的旅行偏好数据（从dh_tripplanner数据库）
        user_profile_db_service = get_user_profile_database_service()
        travel_preferences = await user_profile_db_service.get_user_travel_preferences(state["user_id"])

        logger.info(f"用户 {state['user_id']} 旅行偏好: {travel_preferences}")

        # 分析景点偏好（使用真实数据库数据）
        attraction_template_vars = {
            "core_intent": core_intent,
            "user_profile_text": user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile),
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "travel_preferences": travel_preferences,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }

        attraction_messages = apply_prompt_template(
            "travel_planner/02_attraction_preference_analyzer",
            attraction_template_vars
        )

        # 分析美食偏好（使用真实数据库数据）
        food_template_vars = {
            "core_intent": core_intent,
            "user_profile_text": user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile),
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "travel_preferences": travel_preferences,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days"),
            "travelers": core_intent.get("travelers")
        }
        
        food_messages = apply_prompt_template(
            "travel_planner/03_food_preference_analyzer",
            food_template_vars
        )
        
        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")

        # 流式分析景点偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "attraction_analysis",
            "message": "正在分析景点偏好类型..."
        })

        attraction_preferences = await reasoning_service.analyze_with_structured_output(
            messages=attraction_messages,
            response_format="attraction_preference_schema"
        )

        # 景点偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "attraction_analysis",
            "result": attraction_preferences,
            "message": "景点偏好分析完成"
        })

        # 流式分析美食偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "food_analysis",
            "message": "正在分析美食偏好..."
        })

        food_preferences = await reasoning_service.analyze_with_structured_output(
            messages=food_messages,
            response_format="food_preference_schema"
        )

        # 美食偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "food_analysis",
            "result": food_preferences,
            "message": "美食偏好分析完成"
        })

        # 流式分析住宿偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "accommodation_analysis",
            "message": "正在分析住宿偏好..."
        })

        # 基于用户画像和意图生成住宿偏好（简化版，不调用LLM）
        accommodation_preferences = {
            "budget_range": core_intent.get("budget", {}).get("range", "暂无"),
            "accommodation_type": travel_preferences.get("accommodation_pref", "暂无") if travel_preferences else "暂无",
            "special_requirements": ["免费停车", "安静整洁"] if travel_preferences else ["暂无"],
            "confidence_score": 0.8 if travel_preferences else 0.3
        }

        # 住宿偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "accommodation_analysis",
            "result": accommodation_preferences,
            "message": "住宿偏好分析完成"
        })

        # 构建偏好画像
        preference_profile = {
            "attraction_preferences": attraction_preferences,
            "food_preferences": food_preferences,
            "accommodation_preferences": accommodation_preferences,
            "confidence_score": (
                attraction_preferences.get("confidence_score", 0) +
                food_preferences.get("confidence_score", 0) +
                accommodation_preferences.get("confidence_score", 0)
            ) / 3
        }
        
        state["preference_profile"] = preference_profile
        
        # 添加完成事件
        state = add_event_to_state(state, "preferences_analyzed", {
            "attraction_confidence": attraction_preferences.get("confidence_score", 0),
            "food_confidence": food_preferences.get("confidence_score", 0),
            "overall_confidence": preference_profile["confidence_score"]
        })
        
        logger.info("偏好分析完成")
        
    except Exception as e:
        logger.error(f"偏好分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"偏好分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "preference_analysis",
            "error": str(e)
        })
    
    return state


def should_analyze_multi_city(state: TravelPlanState) -> str:
    """判断是否需要多城市策略分析"""
    core_intent = state.get("core_intent", {})
    destinations = core_intent.get("destinations", [])
    
    if len(destinations) > 1:
        return "multi_city_strategy"
    else:
        return "driving_context"


def should_analyze_driving_context(state: TravelPlanState) -> str:
    """判断是否需要驾驶情境分析"""
    core_intent = state.get("core_intent", {})
    transportation = core_intent.get("transportation", {})
    
    if transportation.get("primary_mode") == "self_driving":
        return "driving_context"
    else:
        return "preference_analysis"


def has_error(state: TravelPlanState) -> str:
    """检查是否有错误"""
    if state.get("has_error", False):
        return "error_handler"
    else:
        return "continue"
